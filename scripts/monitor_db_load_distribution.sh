#!/bin/bash

# Скрипт для мониторинга распределения нагрузки между мастером и репликой PostgreSQL
# Использование: ./monitor_db_load_distribution.sh [интервал_в_секундах]

set -e

# Конфигурация
MASTER_HOST="${DATASOURCE_HOST:-**************}"
MASTER_PORT="${DATASOURCE_PORT:-5432}"
REPLICA_HOST="${DATASOURCE_REPLICA_HOSTS:-**************}"
REPLICA_PORT="${DATASOURCE_REPLICA_PORTS:-5432}"
DB_NAME="${DATASOURCE_DB:-portfolio}"
DB_USER="${DATASOURCE_USERNAME:-portfolio}"
DB_SCHEMA="${DATASOURCE_SCHEMA:-portfolio}"

# Интервал мониторинга (по умолчанию 10 секунд)
INTERVAL=${1:-10}

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функция для логирования
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Функция для проверки доступности базы данных
check_db_connection() {
    local host=$1
    local port=$2
    local db_name=$3
    local user=$4
    local label=$5
    
    if pg_isready -h "$host" -p "$port" -d "$db_name" -U "$user" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Функция для получения статистики подключений
get_connection_stats() {
    local host=$1
    local port=$2
    local db_name=$3
    local user=$4
    
    local query="
    SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections,
        count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction,
        count(*) FILTER (WHERE application_name LIKE '%portfolio%' OR application_name LIKE '%HikariPool%') as portfolio_connections
    FROM pg_stat_activity 
    WHERE datname = '$db_name';"
    
    PGPASSWORD="$DATASOURCE_PASSWORD" psql -h "$host" -p "$port" -U "$user" -d "$db_name" -t -c "$query" 2>/dev/null
}

# Функция для получения статистики запросов
get_query_stats() {
    local host=$1
    local port=$2
    local db_name=$3
    local user=$4
    
    local query="
    SELECT 
        schemaname,
        relname as table_name,
        seq_scan,
        seq_tup_read,
        idx_scan,
        idx_tup_fetch,
        n_tup_ins,
        n_tup_upd,
        n_tup_del
    FROM pg_stat_user_tables 
    WHERE schemaname = '$DB_SCHEMA'
    ORDER BY seq_tup_read + idx_tup_fetch DESC
    LIMIT 10;"
    
    PGPASSWORD="$DATASOURCE_PASSWORD" psql -h "$host" -p "$port" -U "$user" -d "$db_name" -t -c "$query" 2>/dev/null
}

# Функция для получения активных запросов
get_active_queries() {
    local host=$1
    local port=$2
    local db_name=$3
    local user=$4
    
    local query="
    SELECT 
        application_name,
        state,
        query_start,
        substring(query, 1, 100) as query_preview
    FROM pg_stat_activity 
    WHERE datname = '$db_name' 
        AND state = 'active' 
        AND query NOT LIKE '%pg_stat_activity%'
        AND (application_name LIKE '%portfolio%' OR application_name LIKE '%HikariPool%')
    ORDER BY query_start;"
    
    PGPASSWORD="$DATASOURCE_PASSWORD" psql -h "$host" -p "$port" -U "$user" -d "$db_name" -t -c "$query" 2>/dev/null
}

# Функция для отображения статистики
display_stats() {
    local label=$1
    local host=$2
    local port=$3
    local stats=$4
    
    echo -e "\n${BLUE}=== $label ($host:$port) ===${NC}"
    
    if [ -n "$stats" ]; then
        echo "$stats" | while IFS='|' read -r total active idle idle_tx portfolio; do
            total=$(echo "$total" | xargs)
            active=$(echo "$active" | xargs)
            idle=$(echo "$idle" | xargs)
            idle_tx=$(echo "$idle_tx" | xargs)
            portfolio=$(echo "$portfolio" | xargs)
            
            echo "Всего подключений: $total"
            echo "Активных: $active"
            echo "Простаивающих: $idle"
            echo "Простаивающих в транзакции: $idle_tx"
            echo "Portfolio подключений: $portfolio"
        done
    else
        error "Не удалось получить статистику подключений"
    fi
}

# Функция для отображения топ таблиц
display_top_tables() {
    local label=$1
    local stats=$2
    
    echo -e "\n${YELLOW}--- Топ таблиц по активности ($label) ---${NC}"
    
    if [ -n "$stats" ]; then
        echo "$stats" | head -5 | while IFS='|' read -r schema table seq_scan seq_read idx_scan idx_fetch ins upd del; do
            schema=$(echo "$schema" | xargs)
            table=$(echo "$table" | xargs)
            seq_read=$(echo "$seq_read" | xargs)
            idx_fetch=$(echo "$idx_fetch" | xargs)
            
            if [ "$seq_read" != "0" ] || [ "$idx_fetch" != "0" ]; then
                echo "$schema.$table: seq_read=$seq_read, idx_fetch=$idx_fetch"
            fi
        done
    fi
}

# Функция для отображения активных запросов
display_active_queries() {
    local label=$1
    local queries=$2
    
    echo -e "\n${GREEN}--- Активные запросы ($label) ---${NC}"
    
    if [ -n "$queries" ]; then
        echo "$queries" | while IFS='|' read -r app_name state query_start query_preview; do
            app_name=$(echo "$app_name" | xargs)
            state=$(echo "$state" | xargs)
            query_start=$(echo "$query_start" | xargs)
            query_preview=$(echo "$query_preview" | xargs)
            
            echo "[$app_name] $state - $query_start"
            echo "  Query: $query_preview..."
        done
    else
        echo "Нет активных запросов"
    fi
}

# Основная функция мониторинга
monitor_load_distribution() {
    log "Начинаем мониторинг распределения нагрузки между мастером и репликой"
    log "Мастер: $MASTER_HOST:$MASTER_PORT"
    log "Реплика: $REPLICA_HOST:$REPLICA_PORT"
    log "Интервал: $INTERVAL секунд"
    log "Для остановки нажмите Ctrl+C"
    
    # Проверяем доступность баз данных
    if ! check_db_connection "$MASTER_HOST" "$MASTER_PORT" "$DB_NAME" "$DB_USER" "Мастер"; then
        error "Мастер база данных недоступна: $MASTER_HOST:$MASTER_PORT"
        exit 1
    fi
    success "Мастер база данных доступна"
    
    if ! check_db_connection "$REPLICA_HOST" "$REPLICA_PORT" "$DB_NAME" "$DB_USER" "Реплика"; then
        warning "Реплика база данных недоступна: $REPLICA_HOST:$REPLICA_PORT"
        warning "Мониторинг будет выполняться только для мастера"
        REPLICA_AVAILABLE=false
    else
        success "Реплика база данных доступна"
        REPLICA_AVAILABLE=true
    fi
    
    # Основной цикл мониторинга
    while true; do
        clear
        echo -e "${BLUE}Portfolio Database Load Distribution Monitor${NC}"
        echo "$(date '+%Y-%m-%d %H:%M:%S')"
        echo "========================================================"
        
        # Получаем статистику мастера
        master_stats=$(get_connection_stats "$MASTER_HOST" "$MASTER_PORT" "$DB_NAME" "$DB_USER")
        display_stats "МАСТЕР" "$MASTER_HOST" "$MASTER_PORT" "$master_stats"
        
        master_queries=$(get_query_stats "$MASTER_HOST" "$MASTER_PORT" "$DB_NAME" "$DB_USER")
        display_top_tables "Мастер" "$master_queries"
        
        master_active=$(get_active_queries "$MASTER_HOST" "$MASTER_PORT" "$DB_NAME" "$DB_USER")
        display_active_queries "Мастер" "$master_active"
        
        # Получаем статистику реплики, если доступна
        if [ "$REPLICA_AVAILABLE" = true ]; then
            replica_stats=$(get_connection_stats "$REPLICA_HOST" "$REPLICA_PORT" "$DB_NAME" "$DB_USER")
            display_stats "РЕПЛИКА" "$REPLICA_HOST" "$REPLICA_PORT" "$replica_stats"
            
            replica_queries=$(get_query_stats "$REPLICA_HOST" "$REPLICA_PORT" "$DB_NAME" "$DB_USER")
            display_top_tables "Реплика" "$replica_queries"
            
            replica_active=$(get_active_queries "$REPLICA_HOST" "$REPLICA_PORT" "$DB_NAME" "$DB_USER")
            display_active_queries "Реплика" "$replica_active"
            
            # Анализ распределения нагрузки
            echo -e "\n${YELLOW}=== АНАЛИЗ РАСПРЕДЕЛЕНИЯ НАГРУЗКИ ===${NC}"
            
            master_portfolio_conn=$(echo "$master_stats" | cut -d'|' -f5 | xargs)
            replica_portfolio_conn=$(echo "$replica_stats" | cut -d'|' -f5 | xargs)
            
            total_portfolio_conn=$((master_portfolio_conn + replica_portfolio_conn))
            
            if [ "$total_portfolio_conn" -gt 0 ]; then
                master_percentage=$((master_portfolio_conn * 100 / total_portfolio_conn))
                replica_percentage=$((replica_portfolio_conn * 100 / total_portfolio_conn))
                
                echo "Portfolio подключения:"
                echo "  Мастер: $master_portfolio_conn ($master_percentage%)"
                echo "  Реплика: $replica_portfolio_conn ($replica_percentage%)"
                
                if [ "$replica_percentage" -lt 10 ]; then
                    warning "Реплика используется менее чем на 10% - возможно, нужно добавить больше @ReadOnly аннотаций"
                elif [ "$replica_percentage" -gt 80 ]; then
                    warning "Реплика перегружена (>80%) - возможно, нужно пересмотреть распределение"
                else
                    success "Нагрузка распределена разумно"
                fi
            else
                warning "Нет активных Portfolio подключений"
            fi
        fi
        
        echo -e "\n${BLUE}Следующее обновление через $INTERVAL секунд...${NC}"
        sleep "$INTERVAL"
    done
}

# Обработка сигнала прерывания
trap 'echo -e "\n${YELLOW}Мониторинг остановлен${NC}"; exit 0' INT

# Проверяем наличие необходимых утилит
if ! command -v psql &> /dev/null; then
    error "psql не найден. Установите PostgreSQL client."
    exit 1
fi

if ! command -v pg_isready &> /dev/null; then
    error "pg_isready не найден. Установите PostgreSQL client."
    exit 1
fi

# Запускаем мониторинг
monitor_load_distribution
