env: test
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 2
  image:
    repository: harbor-edu.mos.ru/mes-portfolio/portfolio_back
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
  resources:
    #limits:
    #  cpu: 2
    #  memory: 2048Mi
    requests:
      cpu: 2
      memory: 4Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /app/run.sh
      subPath: run.sh
  livenessProbe:
   path: /app/actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
  readinessProbe:
   path: /app/actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
   readnessPeriod: 5

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  name: portfolio-back
  url: mes-kubernetes-test.mos.ru
  pathType: ImplementationSpecific
  path: /portfolio
  servicePort: 80

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    ENABLE_SWAGGER: false
    JAR_APP_PATH: /app/portfolio.jar
    db_host: mesdb-cmnbeta01t
    db_port: 5443
    replica_db_host: mesdb-cmnbeta02t
    replica_port: 5443
    db_pass: portfolio
    clickhouse_host: mesprtf-clhp01t
    clickhouse_port: 9999
    click_login: portfolio
    click_pass: UHPCYBwbbE57
    click_logs_state: false
    click_mv_state: true
    click_api_key: e4af5666-76f7-493f-a324-3cfc8dda71b5
    click_connector_host: https://mes-api-test.mos.ru/prtf-ch-conn/v1/click/
    contingent_api_url: https://mes-api-test.mos.ru/contingent/
    contingent_api_key: a28a0e13-89ad-48ad-b05b-a674e3697c9a
    contingent_source: https://mes-api-test.mos.ru/contingent/persons/
    esz_host: https://mes-api-test.mos.ru/circles/circles
    portfolio_link_qr_code: https://school-test.mos.ru/portfolio/shared-link/
    nsi_api_url: https://mes-api-test.mos.ru/nsi/v1/catalog/get
    nsi_api_key: 8570f457-340b-4af3-a0b1-5f16419c4b79
    nsi_id: 478
    nsi_cache_duration_minutes: 1440
    nsi_cache_maxitems: 1000
    contingent_classes_host_url: https://mes-api-test.mos.ru/contingent/classes/
    organisation_registry_code: 478
    staff_registry_code: 897
    aupd_url: https://mes-api-test.mos.ru/aupd/v1/
    aupd_token: https://school-test.mos.ru/
    aupd_api_key: b36da08b-7fb3-4487-9590-f13671125363
    aupd_cert_name: key_tst.cer
    ATTACHMENTS_CEDS: false
    ceds_url: http://*************/custom-api-2.0/rest/api/document/
    ceds_pass: Hsir2kfkl
    s3_key_id: 4TsmJXejQXDNKSE4n8f6xh
    s3_key_secret: dmqJgubkBxQi7NPARpkRYHNgQfXxhuVNCJtLwPZrJPpn
    s3_bucket_name: s3b-mes-portfolio-test
    s3_address: https://s3-dc.mos.ru
    cron_update_job: "0 0 0/2 ? * *"
    fos_url: https://school-test.mos.ru
    KAFKA_USERNAME: portfolio
    KAFKA_PASSWORD: portfolio
    PROF_URL: https://school-test.mos.ru/api/proforientation/crm/v1
    ESZ_X_API_KEY: Basic VGVzdFVzZXI6VGVzdFBhc3M=
    ESZ_KKY: ebf43c8c-f0b2-4390-bde9-c7895bad4db6
    IMPORT_FILE_MAX_ROWS_COUNT: 10000
    IMPORT_DATA_PARTITION_SIZE: 500
    KAFKA_PRODUCER_ENABLED: true
    KAFKA_CONSUMER_ENABLED: true
    KAFKA_EDUCATION_TOPIC: education.fct.uchi-contest-result.1
    KAFKA_EDUCATION_GROUP_ID: portfolio_group
    KAFKA_BOOTSTRAP_SERVERS: ***********:9092,***********:9092,***********:9092
    KAFKA_TOPIC: messages.from.any.to.eks
    notifications_url: "https://school-test.mos.ru/portfolio/portfolio/student/study?student="
    NOTIFICATIONS_CLICKHOUSE_DAYS_OLD:
    CONNECTION_POOL_SIZE: 20
    REST_TIMEOUT_TIME: 10000
    REST_READ_TIMEOUT_TIME: 10000
    REST_TIMEOUT_TIME_LOW: 5000
    REST_READ_TIMEOUT_TIME_LOW: 5000
    REST_TIMEOUT_TIME_HIGH: 30000
    REST_READ_TIMEOUT_TIME_HIGH: 30000
    MIGRATION_REGIONS: false
    SCHOOL_HOST: https://school-test.mos.ru/avatars/
    SCHOOL_RATING_ENABLED: true
    KAFKA_MAX_POLL_RECORDS: 500
    KAFKA_MAX_POLL_INTERVAL_MS: 300000
    KAFKA_SESSION_TIMEOUT_MS: 45000
    KAFKA_EDUCATION_AUTO_STARTUP: true
    CONTEXT_UPDATE_EDUCATIONS: false
    CONTEXT_ACTUALIZE_PERSONS: false
    MELODY_ENABLED: false
    MELODY_PASSWORD: 'YJ*hsk2o*G'
    xmx: -Xmx2G
    proforientation_api_key: 0e9c32d3-8c59-4d17-a220-240fe38a4fdb
