env: prod-tatarstan
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 1
  image:
    repository: harbor-edu.mos.ru/mes-portfolio/portfolio_back
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
  resources:
    #limits:
    #  cpu: 2
    #  memory: 2048Mi
#    requests:
#      cpu: 8
#      memory: 16Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /app/run.sh
      subPath: run.sh
  livenessProbe:
   path: /app/actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
  readinessProbe:
   path: /app/actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
   readnessPeriod: 5

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  name: portfolio-back
  url: ms-kubernetes-api.16-tat
  pathType: Prefix
  path: /portfolio
  servicePort: 80

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    JAR_APP_PATH: /app/portfolio.jar
    db_host: ms16-db-prtf01p
    db_port: 5432
    replica_db_host: ms16-db-prtf02p
    replica_port: 5432
    db_pass: TayyRfs1
    clickhouse_host: ms16-prtf-chp01p
    clickhouse_port: 9999
    click_login: portfolio
    click_pass: epLHW6EQHNjm
    click_logs_state: false
    click_mv_state: true
    click_api_key: f3764a89-c6af-4d71-9abe-15d6875cda6d
    click_connector_host: https://ms-api.tatar.ru/prtf-ch-conn/v1/click/
    contingent_api_url:  https://ms-api.tatar.ru/contingent/
    contingent_api_key: e52a9570-038f-4695-9b41-ac725e421d76
    contingent_source: https://ms-api.tatar.ru/contingent/persons/
    esz_host: https://ms-api.tatar.ru/circles/circles
    portfolio_link_qr_code: https://ms-edu.tatar.ru/portfolio/shared-link/
    nsi_api_url: https://ms-api.tatar.ru/nsi/v1/catalog/get
    nsi_api_key: c14f7136-59dc-48ab-9cc3-a10a72fee3ee
    nsi_id: 1135
    contingent_classes_host_url: https://ms-api.tatar.ru/contingent/classes/
    organisation_registry_code: 1135
    staff_registry_code: 1221
    aupd_url: https://ms-api.tatar.ru/aupd/v1/
    aupd_token: https://ms-edu.tatar.ru
    aupd_api_key: df1e841f-5c33-42ae-8083-af3cd7217f84
    ATTACHMENTS_CEDS: false
    ceds_url:
    ceds_pass:
    s3_key_id: tat16-prtf
    s3_key_secret: oob8etohB5yi5veeV4
    s3_bucket_name: tat16-prtf
    s3_address: https://s3.tatar.ru
    cron_update_job: "0 0 23 ? * *"
    fos_url: https://ms-edu.tatar.ru
    KAFKA_USERNAME: portfolio
    KAFKA_PASSWORD: derq3W21DKx2
    ESZ_X_API_KEY: Basic RVpEVXNlcjI6bVZjTkFzZEohISE=
    ESZ_KKY: ae5dc9c2-a3d6-4d52-89b2-e97040f5f52d
    IMPORT_FILE_MAX_ROWS_COUNT: 10000
    IMPORT_DATA_PARTITION_SIZE: 500
    KAFKA_EDUCATION_TOPIC: education.fct.uchi-contest-result.1
    KAFKA_EDUCATION_GROUP_ID: portfolio_group
    KAFKA_BOOTSTRAP_SERVERS: ms16-gdm-kfka01p:9092,ms16-gdm-kfka02p:9092,ms16-gdm-kfka03p:9092
    KAFKA_TOPIC: messages.from.any.to.eks
    notifications_url: "https://ms-edu.tatar.ru/portfolio/portfolio/student/study?student="
    NOTIFICATIONS_CLICKHOUSE_DAYS_OLD: 14
    CONNECTION_POOL_SIZE: 120
    REST_TIMEOUT_TIME: 4500
    REST_READ_TIMEOUT_TIME: 4500
    REST_TIMEOUT_TIME_LOW: 5000
    REST_READ_TIMEOUT_TIME_LOW: 5000
    REST_TIMEOUT_TIME_HIGH: 30000
    REST_READ_TIMEOUT_TIME_HIGH: 30000
    MIGRATION_REGIONS: false
    SCHOOL_HOST: https://ms-edu.tatar.ru/avatars/
    SCHOOL_RATING_ENABLED: true
    KAFKA_MAX_POLL_RECORDS: 10
    KAFKA_MAX_POLL_INTERVAL_MS: 300000
    KAFKA_SESSION_TIMEOUT_MS: 45000
    KAFKA_EDUCATION_AUTO_STARTUP: false
    CONTEXT_UPDATE_EDUCATIONS: false
    CONTEXT_ACTUALIZE_PERSONS: false
    MELODY_ENABLED: false
    MELODY_PASSWORD: '@R$JGJ4m2z'
    xmx: -Xmx8G
    proforientation_api_key: 11ff30c6-35fe-4210-ba30-b14b35553858
