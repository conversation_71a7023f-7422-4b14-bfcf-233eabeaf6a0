# Оптимизация распределения нагрузки между мастером и репликой PostgreSQL

## Проблема

Аналитик выявил, что все запросы в системе Portfolio идут только на мастер базы данных (mesdb-prtf01p), в то время как реплика (mesdb-prtf02p) не используется. Это приводит к:

- Перегрузке мастера
- Неэффективному использованию ресурсов реплики
- Потенциальным проблемам с производительностью

## Решение

Была проведена оптимизация системы для корректного распределения read-only запросов на реплику.

### Архитектура распределения нагрузки

Система использует `EzdRoutingDataSource` для маршрутизации запросов:

```java
@Override
protected Object determineCurrentLookupKey() {
    if (BooleanUtils.isTrue(ReadOnlyHolder.get())) {
        Integer chosen = random.randomValue();
        currentReplication.set(chosen);
        return chosen;
    }
    
    //return null means using only master database
    return null;
}
```

### Конфигурация

#### Мастер и реплика настроены в Helm charts:

**Продакшен (values-prod.yaml):**
```yaml
configmap:
  application:
    db_host: **************          # mesdb-prtf01p (мастер)
    db_port: 5432
    replica_db_host: **************  # mesdb-prtf02p (реплика)
    replica_port: 5432
```

#### Application properties:
```properties
spring.datasource.url=jdbc:postgresql://${spring.datasource.originUrl}:${spring.datasource.originPort}/${DATASOURCE_DB:portfolio}
spring.datasource.replica.hosts=${DATASOURCE_REPLICA_HOSTS:${spring.datasource.originUrl}}
spring.datasource.replica.ports=${DATASOURCE_REPLICA_PORTS:5433}
```

### Выполненные оптимизации

#### 1. NsiService
Добавлены `@ReadOnly` аннотации к методам чтения:
- `getOrgNameFromNsiService()`
- `getTeachers()`
- `getName()`
- `getNames()`
- `getResponse()`
- `getOrganizationId()`
- `getByFio()`

#### 2. Repository слой
Добавлены `@ReadOnly` аннотации к:
- `UserRepository` - методы поиска пользователей
- `LearnerHistoryRepository` - методы поиска истории обучения
- `ActionHistoryRepository` - методы поиска истории действий
- `SkippedLessonRepository` - методы поиска пропущенных уроков

#### 3. CrudService
Добавлены `@ReadOnly` аннотации к методам чтения:
- `findRef()`
- `find()`
- `findFirst()`
- `findOptional()`
- `findAll()`
- `findFirstRef()`
- `findAllRefs()`
- `findPersonallyEntity()`

#### 4. Controller слой
Добавлены `@ReadOnly` аннотации к GET методам:
- `ReadableController` - `getAll()`, `getById()`
- `ClickHouseController` - `select()`, `swap()`

### Механизм работы

1. **@ReadOnly аннотация** - помечает методы, которые должны использовать реплику
2. **ReadOnlyMethodAspect** - перехватывает вызовы методов с @ReadOnly и устанавливает `ReadOnlyHolder.set(true)`
3. **EzdRoutingDataSource** - проверяет `ReadOnlyHolder.get()` и направляет запрос на реплику или мастер
4. **WeightedRandom** - обеспечивает распределение нагрузки между репликами

### Конфигурация реплики

Реплика автоматически настраивается как read-only:

```java
@Override
protected HikariConfig createHikariConfig() {
    HikariConfig hikariConfig = super.createHikariConfig();
    hikariConfig.setReadOnly(true);
    return hikariConfig;
}
```

## Тестирование

### Unit тесты
Созданы тесты для проверки:
- Корректности работы `ReadOnlyHolder`
- Логики маршрутизации в `EzdRoutingDataSource`
- Работы `WeightedRandom`
- Конфигурации DataSource

### Интеграционные тесты
Созданы тесты для проверки:
- Реального распределения запросов
- Работы аннотаций @ReadOnly
- Потокобезопасности
- Транзакционного поведения

### Мониторинг

Создан скрипт `scripts/monitor_db_load_distribution.sh` для мониторинга распределения нагрузки:

```bash
./scripts/monitor_db_load_distribution.sh [интервал_в_секундах]
```

Скрипт показывает:
- Количество подключений к мастеру и реплике
- Активные запросы
- Статистику по таблицам
- Процентное распределение нагрузки

## Результаты оптимизации

### До оптимизации:
- 100% запросов на мастер
- 0% запросов на реплику
- Перегрузка мастера

### После оптимизации:
- Write запросы → мастер (100%)
- Read-only запросы → реплика (распределяются согласно весам)
- Снижение нагрузки на мастер
- Эффективное использование реплики

## Рекомендации по дальнейшему использованию

### 1. Добавление @ReadOnly аннотаций
При создании новых методов чтения обязательно добавляйте `@ReadOnly`:

```java
@ReadOnly
public List<Entity> findByCondition(String condition) {
    return repository.findByCondition(condition);
}
```

### 2. Мониторинг
Регулярно запускайте скрипт мониторинга для контроля распределения:

```bash
./scripts/monitor_db_load_distribution.sh 30
```

### 3. Настройка весов
При необходимости можно настроить веса для более точного распределения нагрузки в `DataSourceSettings`.

### 4. Логирование
Для отладки можно включить логирование подключений:

```java
@Override
public Connection getConnection() throws SQLException {
    log.info("getConnection from " + url);
    return super.getConnection();
}
```

## Важные замечания

1. **Транзакции**: Методы в транзакциях всегда используют мастер, даже с @ReadOnly
2. **Консистентность**: Read-only запросы могут читать немного устаревшие данные с реплики
3. **Fallback**: При недоступности реплики запросы автоматически идут на мастер
4. **Аннотации в интерфейсах**: @ReadOnly не работает в MyBatis Mapper интерфейсах

## Мониторинг и алерты

Рекомендуется настроить мониторинг:
- Количество подключений к мастеру и реплике
- Процентное распределение нагрузки
- Время отклика запросов
- Доступность реплики

При отклонении распределения от ожидаемого (например, <10% на реплику) следует проверить:
- Наличие @ReadOnly аннотаций на новых методах
- Корректность работы аспектов
- Доступность реплики
- Конфигурацию маршрутизации
