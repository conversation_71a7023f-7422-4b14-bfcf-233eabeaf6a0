package ru.portfolio.ax.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Where;
import ru.portfolio.ax.model.common.Creatable;
import ru.portfolio.ax.model.common.HasCategoryCode;
import ru.portfolio.ax.model.common.Personalized;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.model.ref.*;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.util.Utils;

import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collections;

@Data
@Entity
@FieldNameConstants
@Where(clause = "is_delete = false")
@EqualsAndHashCode(callSuper = true)
public class Event extends PersonallyEntity implements Creatable, Personalized, HasCategoryCode {

    @Transient
    private SectionRef category;
    @NotNull
    private Integer categoryCode;

    @ManyToOne
    private OlympiadTypeRef levelEvent;

    private String organizators;

    @NotNull
    @ManyToOne
    private OlympiadFormatRef format;

    private String linkedObjectIds;
    private String result;
    private String description;
    private String profile;
    private String location;
    private String participantNumber;
    private String stageEvent;
    private LocalDate startDate;
    private LocalDate endDate;
    private String actionStage;
    private String eventNumber;
    private String ageLimit;
    private LocalDate actionDate;
    private LocalDate stageStartDate;
    private LocalDate stageEndDate;
    private String workCode;

    // list?
    private String parallelsCode;
    private String subspecies;

    @ManyToOne
    private SubcategoryRef subcategory;

    private String locationName;
    @ManyToOne
    private CreationKindRef creationKind;
    @ManyToOne
    private TourismKindRef tourismKind;
    @ManyToOne
    private SportKindRef sportKind;

    @NotNull
    private Long typeCode;
    private String subjectCode;
    private String disciplineCode;
    private Integer eventKindCode;

    @ManyToOne
    private OlympiadLevelRef levelOlympiad;

    @ManyToOne
    private ParticipationTypeRef participationType;

    private Boolean isPresentationCompetence;
    private String participantCategory;
    private String profession;

    private String contestId;
    private ZonedDateTime participatedFrom;
    private ZonedDateTime participatedTo;

    private Double maxScore;
    private Integer schoolId;
    private Integer parallelId;
    private Integer resultId;
    private String resultUrl;

    @Transient
    private final Integer entityTypeCode = 1;

//    private Integer participationStatusCode;
//    @Transient
    @ManyToOne
    private ParticipationStatusRef participationStatus;


    @Override
    public void validate(CrudService crudService) {
        super.validate(crudService);
        crudService.existAllRef(Collections.singleton(categoryCode), SectionRef.class);
    }

    @Override
    public void reachTransient(CrudService crudService) {
        Utils.reachRefs2map(map, getSubjectCode(), SubjectsRef.class, crudService);
        super.reachTransient(crudService);
        setCategory(crudService.findFirstRef(SectionRef.class, categoryCode));
    }
}
