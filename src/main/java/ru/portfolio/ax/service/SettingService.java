package ru.portfolio.ax.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ru.portfolio.ax.configuration.datasource.aspect.WriteOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.ref.LearnerCategoryRef;
import ru.portfolio.ax.model.ref.SectionSettingsRef;
import ru.portfolio.ax.repository.*;
import ru.portfolio.ax.repository.ref.LearnerCategoryRefRepository;
import ru.portfolio.ax.repository.ref.SectionSettingRefRepository;
import ru.portfolio.ax.rest.dto.*;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;
import ru.portfolio.ax.rest.dto.nsi.NsiDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.ext.AUPDService;
import ru.portfolio.ax.service.ext.NsiService;
import ru.portfolio.ax.service.ext.SchoolService;
import ru.portfolio.ax.util.Utils;
import ru.portfolio.ax.util.security.AuthService;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static ru.portfolio.ax.util.RestUtils.convertDirection;
import static ru.portfolio.ax.util.RestUtils.fillPageable;
import static ru.portfolio.ax.util.Utils.join;
import static ru.portfolio.ax.util.Utils.safetyGet;

@Slf4j
@Service
@RequiredArgsConstructor
public class SettingService {

    private final AuthService authService;
    private final AUPDService aupdService;
    private final NsiService nsiService;
    private final SchoolService schoolService;

    private final UserSettingRepository userSettingRepository;
    private final UserRepository userRepository;
    private final ObjectMapper mapper;

    private final UserVisibilitySettingRepository visibilitySettingRepository;

    private final AdministrationSettingRepository administrationSettingRepository;
    private final AdministrationSettingsLogRepository administrationSettingsLogRepository;
    private final SectionSettingRefRepository sectionSettingRefRepository;
    private final LearnerCategoryRefRepository learnerCategoryRefRepository;

    private final AdministratorSettingsFunctionRepository administratorSettingsFunctionRepository;
    private final AdministratorSettingsFunctionLogRepository administratorSettingsFunctionLogRepository;

    private final IndependentDiagnosticVisibleRepository independentDiagnosticVisibleRepository;
    private final ThemeSettingRepository themeSettingRepository;

    private final AvatarSettingRepository avatarSettingRepository;
    private final ObjectMapper objectMapper;

    @Value("${roles.global.adminId}")
    private Long adminId;
    @Value("${roles.global.childId}")
    private Long childId;
    @Value("${roles.global.parentId}")
    private Long parentId;

    @Transactional
    public AvatarSetting saveAvatar(String auth, String personId, Integer avatarType) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(auth);
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        PortfolioException.check(Objects.nonNull(user), PortfolioException.get469());

        AvatarSetting avatarSetting = avatarSettingRepository.findFirstByUserIdAndPersonIdAndIsDelete(
                user.getId(), personId, false).orElse(null);

        if (Objects.nonNull(avatarSetting)) {
            avatarSetting.setAvatarTypeCode(avatarType);
        } else {
            avatarSetting = new AvatarSetting();
            avatarSetting.setPersonId(personId);
            avatarSetting.setAvatarTypeCode(avatarType);
            avatarSetting.setUserId(user.getId());
            avatarSetting.setIsDelete(false);
        }
        return avatarSettingRepository.save(avatarSetting);
    }

    public AvatarDTO getAvatar(String auth, String share, String personId) {
        AvatarDTO avatarDTO = new AvatarDTO();
        AvatarSetting avatarSetting = avatarSettingRepository.findFirstByPersonIdAndIsDeleteOrderByIdDesc(
                 personId, false).orElse(null);

        if (Objects.isNull(avatarSetting)) {
            avatarSetting = new AvatarSetting();
            avatarSetting.setAvatarTypeCode(1);
            avatarSetting.setPersonId(personId);
        }

        avatarDTO.setAvatarSetting(avatarSetting);
        avatarDTO.setAvatar(schoolService.getAvatar(personId, share));
        return avatarDTO;
    }

    @SneakyThrows
    private AdministrationSettingsDTO getDefaultAdministrationSetting() {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("default-administration-setting.json");
        return mapper.readValue(inputStream, AdministrationSettingsDTO.class);
    }

    @WriteOnly
    @Transactional
    public void saveAdministrationSettings(String bearer, AdministrationSettingsDTO request) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        String stf = tokenPayload.getStf();
        if (nonNull(stf)) {
            CurrentUserRolesDTO currentUserRolesGettingResponse = aupdService.getCurrentUserRoles(bearer, tokenPayload.getSub());
            PortfolioException.check(!BooleanUtils.isFalse(currentUserRolesGettingResponse.getCurrentUserRolesGettingResult()), PortfolioException.get555());
            PortfolioException.check(currentUserRolesGettingResponse.getCurrentMeshRoleId().equals(adminId), PortfolioException.get421());
        } else {
            throw PortfolioException.get421();
        }
        for (AdministrationSettingsDTO.Section section : request.getSections()) {
            SectionSettingsRef sectionSettingRef = sectionSettingRefRepository.findById(section.getSectionId()).orElse(null);
            boolean isSectionChanged = false;
            if (nonNull(sectionSettingRef)) {
                List<AdministrationSetting> settings = administrationSettingRepository.findBySectionCode(section.getSectionId());
                settings.sort(Comparator.comparing(AdministrationSetting::getSectionCode));
                Integer requestCode = section.getIsVisible() ? 1 : 2;
                List<String> learnerCategoryCodes =
                        Arrays.asList(StringUtils.splitPreserveAllTokens(section.getLearnerCategoryCodes(), ","));
                for (AdministrationSetting administrationSetting : settings) {
                    AdministrationSetting oldSetting = new AdministrationSetting();
                    BeanUtils.copyProperties(administrationSetting, oldSetting);
                    if (learnerCategoryCodes.contains(administrationSetting.getLearnerCategoryCode().toString())
                            && !administrationSetting.getPermissionCode().equals(requestCode)) {
                        administrationSetting.setPermissionCode(requestCode);
                        isSectionChanged = true;
                    } else if (!learnerCategoryCodes.contains(administrationSetting.getLearnerCategoryCode().toString())
                            && administrationSetting.getPermissionCode().equals(requestCode)) {
                        administrationSetting.setPermissionCode(!section.getIsVisible() ? 1 : 2);
                        isSectionChanged = true;
                    }
                    if (isSectionChanged) {
                        administrationSetting.setAdministratorUserId(stf);
                        administrationSetting.setComment(section.getComment());
                        administrationSetting.setParentSectionId(section.getParentSectionId());
                        administrationSetting.setModificationDate(LocalDateTime.now());
                        administrationSettingRepository.save(administrationSetting);
                    }
                }
                if (isSectionChanged) {
                    logAdminSettingChange(section, stf);
                }
            }
        }
        List<AdministrationSetting> parentAdministrationSetting = administrationSettingRepository.findAllWithoutParent();
        for (AdministrationSetting parent : parentAdministrationSetting) {
            AdministrationSetting oldSetting = new AdministrationSetting();
            BeanUtils.copyProperties(parent, oldSetting);
            List<AdministrationSetting> childSettings =
                    administrationSettingRepository.findByParentAndLearnerCategory(parent.getSectionCode(), parent.getLearnerCategoryCode());
            int permissionCode = 2;
            for (AdministrationSetting child : childSettings) {
                if (child.getPermissionCode().equals(1)) {
                    permissionCode = 1;
                    break;
                }
            }
            parent.setPermissionCode(permissionCode);
            if (!compareSettings(oldSetting, parent)) {
                parent.setAdministratorUserId(stf);
                parent.setModificationDate(LocalDateTime.now());
                administrationSettingRepository.save(parent);
            }
        }
    }

    private boolean compareSettings(AdministrationSetting oldSetting, AdministrationSetting newSetting) {
        oldSetting.setModificationDate(newSetting.getModificationDate());
        if (oldSetting.equals(newSetting)) {
            return true;
        } else {
            return false;
        }
    }

    private void logAdminSettingChange(AdministrationSettingsDTO.Section section, String stf) {
        AdministrationSettingsLog oldLog = administrationSettingsLogRepository.findBySectionId(section.getSectionId());

        String prevCodes;
        if (nonNull(oldLog)) {
            prevCodes = oldLog.getLearnerCategoryCodes();
        } else {
            prevCodes = null;
        }
        section.setLearnerCategoryCodes(sortLearnerCategoryCodes(section.getLearnerCategoryCodes()));
        String actionType;
        if (nonNull(prevCodes) && !prevCodes.equals(section.getLearnerCategoryCodes())) {
            actionType = "change";
        } else {
            actionType = section.getIsVisible() ? "enable" : "disable";
        }

        AdministrationSettingsLog log = new AdministrationSettingsLog();
        log.setSectionId(section.getSectionId());
        log.setLearnerCategoryCodes(section.getLearnerCategoryCodes());
        log.setParentSectionId(section.getParentSectionId());
        log.setAdministratorUserId(stf);
        log.setCreationDate(LocalDateTime.now());
        log.setComment(section.getComment());
        log.setPrevLearnerCategoryCodes(prevCodes);
        log.setActionType(actionType);
        administrationSettingsLogRepository.save(log);
    }

    private String sortLearnerCategoryCodes(String learnerCategoryCodes) {
        if (learnerCategoryCodes == null || StringUtils.isBlank(learnerCategoryCodes)) return null;
        List<Integer> codes = Stream.of(learnerCategoryCodes.split(","))
                .map(String::trim)
                .map(Integer::valueOf)
                .sorted()
                .collect(Collectors.toList());
        return join(codes);
    }

    @Transactional
    public AdministrationSettingsDTO getAdministrationSettings(Integer code, String parallel) {
        Integer learnerCategoryCode = null;
        if (nonNull(parallel)) {
            if (NumberUtils.isCreatable(parallel)) parallel = parallel.concat(" параллель");
            LearnerCategoryRef learnerCategory = learnerCategoryRefRepository.findByValue(parallel);
            if (nonNull(learnerCategory)) learnerCategoryCode = learnerCategory.getCode();
        }

        HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        Cookie aud = safetyGet(() ->
                Utils.first(Arrays.asList(httpRequest.getCookies()), c -> c.getName().equals("aupd_current_role")));
        Cookie share = safetyGet(() ->
                Utils.first(Arrays.asList(httpRequest.getCookies()), c -> c.getName().toLowerCase().equals("share")));
        PortfolioException.check(Objects.nonNull(aud) || Objects.nonNull(share),
                PortfolioException.get442("aupd_current_role"));
        String aupdCurrentRole = (Utils.safeGet(aud, Cookie::getValue));
        Long globalRole = null;
        if (nonNull(aupdCurrentRole)) {
            globalRole = Long.valueOf(aupdCurrentRole.split(":")[1]);
        }
        AdministrationSettingsDTO response = new AdministrationSettingsDTO();
        if (isNull(learnerCategoryCode) && (nonNull(share) ||
                globalRole.equals(childId) || globalRole.equals(parentId))) {
            return getDefaultAdministrationSetting();
        }

        List<AdministrationSetting> settings =
                administrationSettingRepository.findByParams(code, learnerCategoryCode);
        if (settings.isEmpty()) return response;
        List<AdministrationSettingsDTO.Section> sections = new ArrayList<>();
        for (AdministrationSetting setting : settings) {
            AdministrationSettingsDTO.Section section =
                    sections.stream()
                            .filter(x -> x.getSectionId().equals(setting.getSectionCode()) && x.getIsVisible().equals(setting.getPermissionCode().equals(1)))
                            .findFirst()
                            .orElse(new AdministrationSettingsDTO.Section());
            Boolean isExist = true;
            if (isNull(section.getSectionId())) {
                section.setSectionId(setting.getSectionCode());
                isExist = false;
            }
            section.setIsVisible(setting.getPermissionCode().equals(1));
            if (isNull(section.getLearnerCategoryCodes())) {
                section.setLearnerCategoryCodes(nonNull(setting.getLearnerCategoryCode()) ? setting.getLearnerCategoryCode().toString() : null);
            } else {
                section.setLearnerCategoryCodes(section.getLearnerCategoryCodes() + "," + setting.getLearnerCategoryCode());
            }
            section.setParentSectionId(setting.getParentSectionId());
            if (!isExist) sections.add(section);
        }
        if (Objects.nonNull(learnerCategoryCode)) {
            response.setSections(sections);
            response.getSections().forEach(x -> {
                if (x.getLearnerCategoryCodes() != null) {
                    x.setLearnerCategoryCodes(sortLearnerCategoryCodes(x.getLearnerCategoryCodes()));
                }
            });
        } else {
            response.setSections(sections.stream().filter(x -> x.getLearnerCategoryCodes() == null
                    || x.getIsVisible().equals(true)
                    || (x.getIsVisible().equals(false) && x.getLearnerCategoryCodes().length() == 47))
                    .collect(Collectors.toList()));
            response.getSections().forEach(x -> {
                if (x.getLearnerCategoryCodes() != null) {
                    x.setLearnerCategoryCodes(sortLearnerCategoryCodes(x.getLearnerCategoryCodes()));
                }
            });
        }

        response.getSections().sort(Comparator.comparing(AdministrationSettingsDTO.Section::getSectionId));
        return response;
    }

    @Transactional
    public PageImpl getAdministrationSettingsHistory(String bearer, GetAdministrationSettingHistoryRequest request) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        PortfolioException.check(nonNull(user), PortfolioException.get424());
        List<Sort.Order> orders = new ArrayList<>();
        if (nonNull(request.getSort().getSectionCode())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getSectionCode()), "sectionId"));
        }
        if (nonNull(request.getSort().getDate())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getDate()), "creationDate"));
        }
        Pageable pageable = fillPageable(request.getPagingOptions(), orders);
        LocalDateTime start = null;
        if (nonNull(request.getStartDate())) start = LocalDateTime.of(request.getStartDate(), LocalTime.MIN);
        LocalDateTime end = null;
        if (nonNull(request.getEndDate())) end = LocalDateTime.of(request.getEndDate(), LocalTime.MAX);
        List<Integer> sectionCodes = null;
        if (nonNull(request.getSectionCode())) {
            List<SectionSettingsRef> settings = sectionSettingRefRepository.findAllChildsWithRecursive(request.getSectionCode());
            sectionCodes = settings.isEmpty() ? null : settings.stream().map(SectionSettingsRef::getCode).distinct().collect(Collectors.toList());
        }
        if (isNull(sectionCodes)) {
            sectionCodes = sectionSettingRefRepository.findAll().stream().map(SectionSettingsRef::getCode).collect(Collectors.toList());
        }
        Page<AdministrationSettingsLog> page = administrationSettingsLogRepository.findByParams(sectionCodes, start, end, pageable);
        if (page.getTotalElements() == 0) {
            return new PageImpl(Collections.emptyList(), pageable, page.getTotalElements());
        }

        List<String> ids = page.getContent().stream().map(AdministrationSettingsLog::getAdministratorUserId)
                .distinct().collect(Collectors.toList());
        List<NsiDTO.Response> nsiResponses = null;
        try {
            nsiResponses = nsiService.getEmployee(ids);
        } catch (Exception ignored) {
        }

        List<AdministrationSettingsHistoryDTO> responseContent = new ArrayList<>();
        List<NsiDTO.Response> finalNsiResponses = nsiResponses;
        page.forEach(log ->
        {
            AdministrationSettingsHistoryDTO section = new AdministrationSettingsHistoryDTO();
            section.setId(log.getId());
            section.setSectionId(log.getSectionId());
            section.setLearnerCategoryCodes(log.getLearnerCategoryCodes());
            section.setPrevLearnerCategoryCodes(log.getPrevLearnerCategoryCodes());
            section.setComment(log.getComment());
            section.setCreationDate(log.getCreationDate());
            section.setParentSectionId(log.getParentSectionId());
            section.setActionType(log.getActionType());
            if (nonNull(finalNsiResponses)) {
                UserContextDTO.Info.UserFio fio = finalNsiResponses.stream()
                        .filter(x -> x.getGlobalId().equals(Long.valueOf(log.getAdministratorUserId())))
                        .map(UserContextDTO.Info.UserFio::build)
                        .findFirst().orElse(null);
                if (nonNull(fio)) {
                    String resultFio = fio.getLastName() + " " + fio.getFirstName().substring(0, 1) + ".";
                    if (nonNull(fio.getPatronymic())
                            && !StringUtils.isBlank(fio.getPatronymic())
                            && !StringUtils.isEmpty(fio.getPatronymic())) {
                        resultFio = resultFio + fio.getPatronymic().substring(0, 1) + ".";
                    }
                    section.setAdministrationFIO(resultFio);
                }
            }
            responseContent.add(section);
        });
        return new PageImpl(responseContent, pageable, page.getTotalElements());
    }

    @Transactional
    public void saveAdministratorSettings(SaveAdministratorSettingsRequest request) {
        for (AdministratorSettingFunctionDTO admSetting : request.getAdmSettings()) {
            AdministratorSettingsFunction setting =
                    administratorSettingsFunctionRepository.findByFunctionType(admSetting.getFunctionType());
            if (nonNull(setting) && setting.getPermissionCode() != (admSetting.getIsVisible() ? 1 : 2)) {
                setting.setPermissionCode(admSetting.getIsVisible() ? 1 : 2);
                setting.setAdministratorUserId(request.getAdministratorUserId());
                setting.setModificationDate(LocalDateTime.now());
                administratorSettingsFunctionRepository.save(setting);

                AdministratorSettingsFunctionLog log = new AdministratorSettingsFunctionLog();
                log.setActionType(admSetting.getIsVisible() ? "enable" : "disable");
                log.setAdministratorUserId(request.getAdministratorUserId());
                log.setCreationDate(LocalDateTime.now());
                log.setFunctionType(setting.getFunctionType());
                administratorSettingsFunctionLogRepository.save(log);
            }
        }
    }

    @WriteOnly
    @Transactional
    public List<AdministratorSettingFunctionDTO> getAdministratorSettings(String functionType) {
        List<AdministratorSettingsFunction> settings = new ArrayList<>();
        if (nonNull(functionType)) {
            AdministratorSettingsFunction setting =
                    administratorSettingsFunctionRepository.findByFunctionType(functionType);
            settings.add(setting);
        } else {
            settings = administratorSettingsFunctionRepository.findAll();
        }
        List<AdministratorSettingFunctionDTO> response = new ArrayList<>();
        for (AdministratorSettingsFunction setting : settings) {
            AdministratorSettingFunctionDTO dto = new AdministratorSettingFunctionDTO();
            dto.setFunctionType(setting.getFunctionType());
            dto.setIsVisible(setting.getPermissionCode() == 1);
            response.add(dto);
        }
        return response;
    }

    @WriteOnly
    @Transactional
    public Page<AdministratorSettingFunctionLogDTO> getAdministratorSettingFunctionLog(String bearer, GetAdministratorSettingFunctionLogRequest request) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        PortfolioException.check(nonNull(user), PortfolioException.get424());
        List<Sort.Order> orders = new ArrayList<>();
        if (nonNull(request.getSort().getSectionCode())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getSectionCode()), "sectionId"));
        }
        if (nonNull(request.getSort().getDate())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getDate()), "creationDate"));
        }
        Pageable pageable = fillPageable(request.getPagingOptions(), orders);
        LocalDateTime start = null;
        if (nonNull(request.getStartDate())) start = LocalDateTime.of(request.getStartDate(), LocalTime.MIN);
        LocalDateTime end = null;
        if (nonNull(request.getEndDate())) end = LocalDateTime.of(request.getEndDate(), LocalTime.MAX);
        Page<AdministratorSettingsFunctionLog> page = administratorSettingsFunctionLogRepository.findByParams(start, end, pageable);
        if (page.getTotalElements() == 0) {
            return new PageImpl(Collections.emptyList(), pageable, page.getTotalElements());
        }
        List<String> administratorIds = page.getContent()
                .stream()
                .map(AdministratorSettingsFunctionLog::getAdministratorUserId)
                .collect(Collectors.toList());
        List<NsiDTO.Response> nsiResponse;
        try {
            nsiResponse = nsiService.getEmployee(administratorIds);
        } catch (Exception e) {
            nsiResponse = new ArrayList<>();
        }
        Map<String, String> fioMap = nsiResponse.stream()
                .collect(Collectors.toMap(k -> k.getGlobalId().toString(),
                        v -> v.getSurname() + " " + v.getFirstName() + " " + v.getSecondName()));
        List<AdministratorSettingFunctionLogDTO> responseContent = new ArrayList<>();
        for (AdministratorSettingsFunctionLog log : page.getContent()) {
            AdministratorSettingFunctionLogDTO dto = new AdministratorSettingFunctionLogDTO();
            dto.setId(log.getId());
            dto.setActionType(log.getActionType().equals("enable"));
            dto.setCreationDate(log.getCreationDate());
            String fio = fioMap.get(log.getAdministratorUserId());
            dto.setAdministratorFIO(nonNull(fio) ? fio : "Не доступен");
            responseContent.add(dto);
        }
        return new PageImpl(responseContent, pageable, page.getTotalElements());
    }

    public void changeIndependentDiagnosticVisible(ChangeIndependentDiagnosticVisibleDTO request) {
        List<String> recordIds = request.getChangeCard().stream()
                .map(ChangeIndependentDiagnosticVisibleDTO.ChangeCard::getRecordId)
                .collect(Collectors.toList());

        List<IndependentDiagnosticVisible> diagnostics = independentDiagnosticVisibleRepository.findByPersonIdAndRecordIdIn(request.getPersonId(), recordIds);
        for (IndependentDiagnosticVisible diagnostic : diagnostics) {
            ChangeIndependentDiagnosticVisibleDTO.ChangeCard changeCard = request.getChangeCard().stream()
                    .filter(x -> x.getRecordId().equals(diagnostic.getRecordId()))
                    .findFirst().orElse(null);
            if (!diagnostic.getIsVisible().equals(changeCard.getIsVisible())) {
                diagnostic.setIsVisible(changeCard.getIsVisible());
                diagnostic.setChangeDate(LocalDateTime.now());
            }
        }
        independentDiagnosticVisibleRepository.saveAll(diagnostics);
    }

    public ThemeSettingsResponseDTO setThemeSettings(String bearer, String personId, ThemeSettingsDTO themeSettings) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        PortfolioException.check(tokenPayload.getMsh().equals(personId), PortfolioException.get421());
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        PortfolioException.check(nonNull(user), PortfolioException.get424());

        JsonNode node = objectMapper.valueToTree(themeSettings);
        List<ThemeSettings> themeByValue = themeSettingRepository.findAllByValue(node);
        ThemeSettings themeSetting;
        if (themeByValue.isEmpty()) {
            themeSetting = new ThemeSettings();
            themeSetting.setValue(node);
            themeSettingRepository.save(themeSetting);
        } else {
            themeSetting = themeByValue.stream().findFirst().orElse(null);
        }

        UserSetting userSetting = userSettingRepository.findFirstByUserIdAndPersonIdOrderByIdDesc(tokenPayload.getSub(), personId);
        userSetting.setThemeSetting(themeSetting.getId());
        userSettingRepository.save(userSetting);

        List<User> parents = userRepository.findAllByChildrenContains(personId);
        for (User parent : parents) {
            UserSetting parentUserSetting = userSettingRepository.findFirstByUserIdAndPersonIdOrderByIdDesc(parent.getAupdId(), personId);
            if (nonNull(parentUserSetting)) {
                parentUserSetting.setThemeSetting(themeSetting.getId());
                userSettingRepository.save(parentUserSetting);
            }
        }

        ThemeSettingsResponseDTO themeResponse = new ThemeSettingsResponseDTO();
        themeResponse.setId(userSetting.getId());
        themeResponse.setPersonId(personId);
        themeResponse.setThemeSetting(themeSetting.getId());
        themeResponse.setThemeSettings(themeSetting.getValue());
        themeResponse.setCreationDate(userSetting.getCreationDate());
        themeResponse.setSettings(userSetting.getSetting());
        return themeResponse;
    }
}
