package ru.portfolio.ax.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.common.AbstractRefEntity;
import ru.portfolio.ax.model.common.Linkable;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.model.dto.*;
import ru.portfolio.ax.model.enums.*;
import ru.portfolio.ax.model.ref.*;
import ru.portfolio.ax.repository.*;
import ru.portfolio.ax.repository.ref.SubjectsRefRepository;
import ru.portfolio.ax.rest.dto.*;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;
import ru.portfolio.ax.rest.dto.contingent.EducationDTO;
import ru.portfolio.ax.rest.dto.contingent.PersonDTO;
import ru.portfolio.ax.rest.dto.enums.SettingSectionType;
import ru.portfolio.ax.rest.dto.nsi.NsiDTO;
import ru.portfolio.ax.rest.exception.PortfolioCodifiedEnum;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.ext.ContingentService;
import ru.portfolio.ax.service.ext.NsiService;
import ru.portfolio.ax.util.Utils;
import ru.portfolio.ax.util.personUtils.PersonUtils;
import ru.portfolio.ax.util.security.AuthService;
import ru.portfolio.ax.util.shareLinkUtils.ShareLinkUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.Comparator.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.math3.util.Precision.round;
import static ru.portfolio.ax.model.enums.PersonallyEntityEnum.*;
import static ru.portfolio.ax.util.Utils.safeGet;
import static ru.portfolio.ax.util.Utils.safetyGet;
import static ru.portfolio.ax.util.mapper.SubjectPerfomanceMapper.mapSubjectDto;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClickHouseService {
    private final NsiService nsiService;
    private final AuthService authService;
    private final CrudService crudService;
    private final ContingentService contingentService;
    private final ClickHouseRepository clickHouseRepository;
    private final SubjectsRefRepository subjectsRefRepository;
    private final PersonUpdateRepository personUpdateRepository;
    private final LearnerOlympiadRepository learnerOlympiadRepository;
    private final PersonAchievementRepository personAchievementRepository;
    private final EmplRepository emplRepository;
    private final EventRepository eventRepository;
    private final OlympiadOutcomeRepository olympiadOutcomeRepository;
    private final RewardRepository rewardRepository;
    private final ProjectRepository projectRepository;
    private final SportRewardRepository sportRewardRepository;
    private final AffRepository affRepository;
    private final GIAWorldskillsRepository giaWorldskillsRepository;
    private final UserRepository userRepository;
    private final LinkedObjectRepository linkedObjectRepository;
    private final ObjectMapper objectMapper;
    private final PersonAttachmentMetadataRepository personAttachmentMetadataRepository;
    private final IndependentDiagnosticVisibleRepository independentDiagnosticVisibleRepository;
    private final GiaExamRepository giaExamRepository;

    private final LearnerOlympiadSubjectRepository learnerSubjectRepository;
    private final LearnerOlympiadParallelsRepository learnerParallelsRepository;

    private final ShareLinkUtils shareLinkUtils;
    private final PersonUtils personUtils;

    private final UserVisibilitySettingRepository visibilitySettingRepository;

    @Value("${roles.global.employeeId}")
    private Long employeeId;

    @Value("${roles.global.adminId}")
    private Long adminId;

    @Value("${roles.global.parentId}")
    private Long parentId;

    @Value("${general-rating.best.enabled}")
    private Boolean bestOfEnabled;

    @Value("${clickhouse.use-material-views}")
    private Boolean useMaterialViews;
    @Value("${diagnostic.school-rating.enabled}")
    private Boolean schoolRatingEnabled;

    @PostConstruct
    public void init() {
        log.info("Проверка хэша clickHouseRepositoryImpl: " + this.clickHouseRepository.toString());
        log.info("Проверка хэша clickHouseConnection: " + this.clickHouseRepository.getClickHouseConnection());
    }

    @SneakyThrows
    public Object select(String table) {
        return clickHouseRepository.select(table);
    }

    public List<ExamInfoDTO> getExams(String guid, ExamDTO exam) {
        List<GiaExam> giaExams =
                giaExamRepository.findAllByPersonIdInAndEventDateAndSubjectId(getPersonIds(guid), exam.getExamDate(),
                        exam.getExamSubjectId().longValue());
        return giaExams.stream().map(ExamInfoDTO::build).collect(Collectors.toList());
    }

    public AverageAcademicPerformanceDTO getAveragePerformance(String personId) {
        if (useMaterialViews) {
            return clickHouseRepository.findAveragePerformanceByGuidMAT(personId);
        } else {
            return clickHouseRepository.findAveragePerformanceByGuid(personId);
        }
    }

    public List<AcademicPerformanceBySchoolDTO> progress(String guid, LocalDate startDate, LocalDate endDate, String stage, String share) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            //PortfolioException.check(Objects.nonNull(shareLink) && shareLink.getPerformance(), PortfolioException.get461());
        }

        PortfolioException.check((nonNull(stage) || (nonNull(startDate) && nonNull(endDate))), PortfolioException.get442("startDate, endDate"));
        if (nonNull(startDate)) {
            PortfolioException.check(startDate.isBefore(endDate), PortfolioException.get443());
        }

        if (Objects.nonNull(stage)) {
            switch (stage) {
                case "year": {
                    int currentYear = Calendar.getInstance().get(Calendar.YEAR);
                    if (LocalDate.now().isBefore(LocalDate.of(currentYear, Month.AUGUST, 31))
                            || LocalDate.now().isEqual(LocalDate.of(currentYear, Month.AUGUST, 31))) {
                        startDate = LocalDate.of(currentYear - 1, Month.AUGUST, 31);
                        endDate = LocalDate.of(currentYear, Month.AUGUST, 31);
                    } else {
                        startDate = LocalDate.of(currentYear, Month.AUGUST, 31);
                        endDate = LocalDate.of(currentYear + 1, Month.AUGUST, 31);
                    }
                    break;
                }
                case "current": {
                    break;
                }
                default: {
                    throw PortfolioException.get446("stage", stage);
                }
            }
        }

        LocalDate finalDate = endDate.isAfter(LocalDate.now()) ? LocalDate.now() : endDate;

        Collection<String> personIds = getPersonIds(guid);

        List<ProgressDTO> progressLessonList = clickHouseRepository
                .findAllByGuidAndBetweenDateAndEventType(personIds, startDate, endDate, EventType.LESSON.getCode());
        List<ProgressDTO> absenceLessonList = clickHouseRepository
                .findAllByGuidAndBetweenDateAndEventType(personIds, startDate, endDate, EventType.SKIPPED_LESSON.getCode());

        List<ProgressDTO> scoreList = clickHouseRepository
                .findAllByGuidAndBetweenDateAndEventTypeAndNotMarkedEventType(
                        personIds, startDate, endDate, EventType.SCORE.getCode(), MarkedEventType.CHECK.getCode());
        List<ProgressDTO> finalScoreList = clickHouseRepository
                .findAllByGuidAndBetweenDateAndEventTypeMarkedEventTypeAndAttestationPeriodBetween(
                        personIds, startDate, endDate, EventType.SCORE.getCode(), MarkedEventType.CHECK.getCode());

        HashMap<Integer, AcademicPerformanceBySchoolDTO> subjectByIdMap = new HashMap<>();

        progressLessonList.forEach(progressDTO -> {
            int subjectId = progressDTO.getSubjectId();
            if (!subjectByIdMap.containsKey(subjectId)) {
                List<ProgressDTO> filteredProgressLessonList = progressLessonList.stream()
                        .filter(dto -> dto.getSubjectId().equals(subjectId)).collect(Collectors.toList());
                List<ProgressDTO> filteredAbsenceLessonList = absenceLessonList.stream()
                        .filter(dto -> dto.getSubjectId().equals(subjectId)).collect(Collectors.toList());
                List<ProgressDTO> filteredScoreList = scoreList.stream()
                        .filter(dto -> dto.getMarkedSubjectId().equals(subjectId)).collect(Collectors.toList());
                List<ProgressDTO> filteredFinalScoreList = finalScoreList.stream()
                        .filter(dto -> dto.getMarkedSubjectId().equals(subjectId)).collect(Collectors.toList());

                subjectByIdMap.put(subjectId, mapSubjectDto(progressDTO, filteredProgressLessonList,
                        filteredAbsenceLessonList, finalDate, filteredScoreList, filteredFinalScoreList));
            }
        });

        return Lists.newArrayList(subjectByIdMap.values());
    }

    private List<UUID> getPersonIdsAsUuid(String id) {
        return Utils.transform(getPersonIds(id), UUID::fromString);
    }

    @ReadOnly
    @Transactional
    public Collection<String> getPersonIds(String id) {
        return personUtils.getPersonIds(id);
    }

    public OlympiadFullInfoDTO getOlympiadInfo(String bearer, OlympiadsDTO olympiadDTO, String share) {
        ShareLink shareLink = parseCookie(share);
        String olympiadName = SettingSectionType.OLYMPIAD.name().toLowerCase();

        if (Objects.nonNull(shareLink) && Objects.nonNull(shareLink.getUser())) {
            //PortfolioException.check(shareLink.getOlympiad(), PortfolioException.get461());
            UserVisibilitySetting visibilitySetting =
                    isNull(olympiadDTO.getOlympiad()) ?
                            visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordUuid(
                                    UUID.fromString(olympiadDTO.getPersonId()), shareLink.getUser().getId(),
                                    olympiadName, olympiadDTO.getUploadOlympiad().getRecordId()) :
                            visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordId(
                                    UUID.fromString(olympiadDTO.getPersonId()), shareLink.getUser().getId(),
                                    olympiadName, olympiadDTO.getOlympiad().getOlympiadId()
                            );
            PortfolioException.check(isNull(visibilitySetting), PortfolioException.get461());
        }
        Collection<UUID> personIds = Objects.nonNull(olympiadDTO.getOlympiad()) ?
                getPersonIdsAsUuid(olympiadDTO.getOlympiad().getPersonId()) :
                getPersonIdsAsUuid(olympiadDTO.getUploadOlympiad().getPersonId());

        User user = Objects.nonNull(bearer) ?
                userRepository.findFirstByAupdId(authService.getTokenPayload(bearer).getSub())
                        .orElseThrow(PortfolioException.notFound(User.class))
                : Utils.safeGet(shareLink, ShareLink::getUser);

        OlympiadFullInfoDTO result = new OlympiadFullInfoDTO();
        if (Objects.nonNull(olympiadDTO.getUploadOlympiad())) {
            OlympiadDTO.OlympiadClickDTO record = clickHouseRepository.findByRecordId(olympiadDTO.getUploadOlympiad().getRecordId());
            PortfolioException.check(Objects.nonNull(record), PortfolioException.get566());
            extendOlympiadInfo(result, record);

            if (Objects.nonNull(user)) {
                UserVisibilitySetting setting = visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordUuid(
                        UUID.fromString(olympiadDTO.getPersonId()), user.getId(), olympiadName, record.getRecordId());
                Utils.safeSet(setting, s -> result.setSettingId(s.getId()));
            }
        } else if (Objects.nonNull(olympiadDTO.getOlympiad())) {
            LearnerOlympiad learner = learnerOlympiadRepository.findFirstByPersonIdInAndId(personIds, olympiadDTO.getOlympiad().getOlympiadId());

            PortfolioException.check(Objects.nonNull(learner), PortfolioException.get439());
            extendOlympiadInfo(result, Objects.requireNonNull(learner));
            Optional.ofNullable(learner.getDataSource()).ifPresent(x ->
                    result.setCreator(getCreator(bearer, x.getCode(), learner.getCreatorId().toString())));
            personAttachmentMetadataRepository.findAllByOlympiadIdAndDeletedIs(learner.getId(), false).forEach(x -> {
                OlympiadFullInfoDTO.Attachment attachment = new OlympiadFullInfoDTO.Attachment();
                attachment.setId(x.getId());
                attachment.setIsDelete(x.isDeleted());
                attachment.setName(x.getName());
                result.getAttachment().add(attachment);
            });

            if (Objects.nonNull(user)) {
                UserVisibilitySetting setting = visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordId(
                        UUID.fromString(olympiadDTO.getPersonId()), user.getId(), olympiadName, learner.getId());
                Utils.safeSet(setting, s -> result.setSettingId(s.getId()));
            }
        }

        return result;
    }

    private OlympiadFullInfoDTO extendOlympiadInfo(OlympiadFullInfoDTO olympiadDTO, LearnerOlympiad learnerOlympiad) {
//        olympiadDTO.setMark(clickDTO.getMark());
        olympiadDTO.setId(learnerOlympiad.getId());
        olympiadDTO.setFormat(learnerOlympiad.getOlympiadFormat());
        olympiadDTO.setOrganizators(learnerOlympiad.getOrganizators());
        olympiadDTO.setName(learnerOlympiad.getName());
        olympiadDTO.setDate(learnerOlympiad.getParticipationDate());
        olympiadDTO.setId(learnerOlympiad.getId());
        olympiadDTO.setType(learnerOlympiad.getOlympiadType());
        olympiadDTO.setResult(learnerOlympiad.getOlympiadResult());
        olympiadDTO.setLevel(learnerOlympiad.getOlympiadLevel());
        olympiadDTO.setSubjects(learnerOlympiad.getLearnerOlympiadSubjects().stream().map(LearnerOlympiadSubject::getSubject).collect(Collectors.toList()));
        olympiadDTO.setParallel(learnerOlympiad.getLearnerOlympiadParallels().stream().map(LearnerOlympiadParallels::getParallels).collect(Collectors.toList()));
        olympiadDTO.setSource(learnerOlympiad.getDataSource());
        olympiadDTO.setCreationDate(learnerOlympiad.getCreationDate());
        olympiadDTO.setModificationDate(learnerOlympiad.getModificationDate());
        olympiadDTO.setIsDelete(learnerOlympiad.getIsDelete());
        olympiadDTO.setProfile(learnerOlympiad.getProfile());
        olympiadDTO.setAdditionalResultInfo(learnerOlympiad.getAdditionalResultInfo());
        return olympiadDTO;
    }

    private OlympiadFullInfoDTO extendOlympiadInfo(OlympiadFullInfoDTO olympiadDTO, OlympiadDTO.OlympiadClickDTO clickDTO) {
//        olympiadDTO.setMark(clickDTO.getMark());
        olympiadDTO.setRecordId(clickDTO.getRecordId());
        Optional.ofNullable(clickDTO.getFormat()).ifPresent(x -> {
            olympiadDTO.setFormat(crudService.findFirstRefByValue(OlympiadFormatRef.class, clickDTO.getFormat()));
        });
        olympiadDTO.setFormat(crudService.findFirstRefByValue(OlympiadFormatRef.class, clickDTO.getFormat()));
        olympiadDTO.setOrganizators(clickDTO.getOrganizators());
        olympiadDTO.setName(clickDTO.getName());
        olympiadDTO.setDate(clickDTO.getDate());
        olympiadDTO.setId(clickDTO.getOlympiadId());
        olympiadDTO.setDateTime(clickDTO.getEventTime());
        olympiadDTO.setType(crudService.findFirstRefByValue(OlympiadTypeRef.class, clickDTO.getType()));
        olympiadDTO.setResult(crudService.findFirstRefByValue(OlympiadResultRef.class, clickDTO.getStatus().split(",")[0]));
        Set<Integer> collect = Arrays.stream(clickDTO.getParallels().split("-"))
                .map(String::trim).map(Integer::parseInt).collect(Collectors.toSet());
        collect.forEach(x -> olympiadDTO.getParallel().add(crudService.findFirstRef(ParallelsRef.class, x)));
        olympiadDTO.setLevel(crudService.findFirstRefByValue(OlympiadLevelRef.class, clickDTO.getLevel()));
        olympiadDTO.setSubjects(subjectsRefRepository.findByCodeIn(Utils.split(clickDTO.getSubjects()).stream().map(Integer::valueOf).collect(Collectors.toList())));
        olympiadDTO.setSource(crudService.findFirstRefByValue(DataSourceRef.class, "Олимпиады"));
        olympiadDTO.setMark(clickDTO.getMark());
        return olympiadDTO;
    }

    @Transactional
    public void deleteOlympiad(Long id) {
        LearnerOlympiad learnerOlympiad = learnerOlympiadRepository.findById(id)
                .orElseThrow(() -> PortfolioException.get(PortfolioCodifiedEnum.E459));

        learnerOlympiad.setIsDelete(Boolean.TRUE);
        learnerOlympiadRepository.save(learnerOlympiad);
    }


    public List<OlympiadDTO> getOlympiads(String id, String bearer, String share) {
        ShareLink shareLink = parseCookie(share);
        if (Objects.nonNull(share) && Objects.nonNull(shareLink)) {
            //PortfolioException.check(shareLink.getOlympiad(), PortfolioException.get461());
        }
        List<Long> hiddenIds = new LinkedList<>();
        List<UUID> hiddenUUIDs = new LinkedList<>();
        Map<Object, Long> visibleSettings = new HashMap<>();

        User user = Objects.nonNull(bearer) ? userRepository.findFirstByAupdId(authService.getTokenPayload(bearer).getSub())
                .orElseThrow(PortfolioException.notFound(User.class)) : Utils.safeGet(shareLink, ShareLink::getUser);
        if (Objects.nonNull(user)) {
            visibilitySettingRepository
                    .findAllByPersonIdAndUserIdAndSection(UUID.fromString(id), user.getId(),
                            SettingSectionType.OLYMPIAD.name().toLowerCase()).forEach(a -> {
                                if (isNull(a.getRecordId())) {
                                    hiddenUUIDs.add(a.getRecordUuid());
                                    visibleSettings.put(a.getRecordUuid(), a.getId());
                                } else {
                                    hiddenIds.add(a.getRecordId());
                                    visibleSettings.put(a.getRecordId(), a.getId());
                                }
                            }
                    );
        }

        List<UUID> uuidList = getPersonIdsAsUuid(id);

        List<LearnerOlympiad> learners = isNull(share) ? learnerOlympiadRepository.findAllByPersonIdInAndIsDelete(uuidList, false) :
                learnerOlympiadRepository.findAllByPersonIdInAndIsDeleteAndIdNotIn(uuidList, false, hiddenIds);
        List<OlympiadDTO.OlympiadClickDTO> olympiads = clickHouseRepository.findAllByGuidAndEventType(
                uuidList.stream().map(UUID::toString).collect(Collectors.toList()), EventType.OLYMPIAD.getCode(),
                isNull(share) ? null : hiddenUUIDs);

        List<OlympiadDTO> olympiadDTOList = new LinkedList<>();

        for (LearnerOlympiad learner : learners) {
            OlympiadDTO olympiadDTO = new OlympiadDTO();
            olympiadDTO.setId(learner.getId());
            olympiadDTO.setDate(learner.getParticipationDate());
            olympiadDTO.setName(learner.getName());
            olympiadDTO.setType(learner.getOlympiadType());
            olympiadDTO.setSource(learner.getDataSource());
            olympiadDTO.setResult(learner.getOlympiadResult());
            olympiadDTO.setSubjects(Utils.transform(learner.getLearnerOlympiadSubjects(), LearnerOlympiadSubject::getSubject));
            olympiadDTO.setCreationDate(learner.getCreationDate());

            Optional.ofNullable(learner.getCreatorId()).map(UUID::toString).ifPresent(olympiadDTO::setCreatorId);
            olympiadDTO.setDataSource(safeGet(learner.getDataSource(), AbstractRefEntity::getCode));
            olympiadDTO.setId(learner.getId());
            olympiadDTOList.add(olympiadDTO);
        }
        fillOlympiadCreator(bearer, olympiadDTOList);

        DataSourceRef sourceRef = crudService.findFirstRefByValue(DataSourceRef.class, "Олимпиады");
        Map<String, OlympiadTypeRef> allTypes = new HashMap<>();
        Map<String, OlympiadResultRef> allResults = new HashMap<>();
        Map<Integer, SubjectsRef> allSubjects = new HashMap<>();
        crudService.findAllRefs(OlympiadTypeRef.class).forEach(t -> allTypes.put(t.getValue(), t));
        crudService.findAllRefs(OlympiadResultRef.class).forEach(t -> allResults.put(t.getValue(), t));
        crudService.findAllRefs(SubjectsRef.class).forEach(t -> allSubjects.put(t.getCode(), t));


        for (OlympiadDTO.OlympiadClickDTO learner : olympiads) {
            OlympiadDTO olympiadDTO = new OlympiadDTO();
            olympiadDTO.setId(learner.getOlympiadId());
            olympiadDTO.setDate(learner.getDate());
            olympiadDTO.setName(learner.getName());
            olympiadDTO.setType(allTypes.get(learner.getType()));
            olympiadDTO.setResult(allResults.get(learner.getStatus()));

            List<SubjectsRef> refs = Utils.split(learner.getSubjects()).stream().map(Integer::valueOf)
                    .collect(Collectors.toList())
                    .stream().map(allSubjects::get)
                    .collect(Collectors.toList());

            olympiadDTO.setSubjects(refs);
            olympiadDTO.setSource(sourceRef);
            olympiadDTO.setCreationDate(learner.getEventTime());
            olympiadDTO.setDateTime(learner.getEventTime());
            olympiadDTO.setRecordId(learner.getRecordId());
            olympiadDTOList.add(olympiadDTO);
        }

        olympiadDTOList.forEach(a -> a.setSettingId(visibleSettings.getOrDefault(isNull(a.getId()) ? a.getRecordId() : a.getId(), null)));

        return olympiadDTOList.stream().sorted(Comparator.comparing(OlympiadDTO::getDate).reversed()).collect(Collectors.toList());
    }

    public AchievementFullInfoDTO getAchievement(AchievementsDTO achievementDTO, String token, String share) {
        ShareLink shareLink = parseCookie(share);
        if (Objects.nonNull(shareLink) && Objects.nonNull(shareLink.getUser())) {
            //PortfolioException.check(shareLink.getAchievements(), PortfolioException.get461());
            UserVisibilitySetting visibilitySetting =
                    isNull(achievementDTO.getAchievement()) ?
                            visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordUuid(
                                    UUID.fromString(achievementDTO.getPersonId()), shareLink.getUser().getId(),
                                    SettingSectionType.OLYMPIAD.name().toLowerCase(), achievementDTO.getUploadAchievement().getRecordId()) :
                            visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordId(
                                    UUID.fromString(achievementDTO.getPersonId()), shareLink.getUser().getId(),
                                    SettingSectionType.OLYMPIAD.name().toLowerCase(), achievementDTO.getAchievement().getAchievementId()
                            );
            PortfolioException.check(isNull(visibilitySetting), PortfolioException.get461());
        }
        AchievementsDTO.Achievement achievement = achievementDTO.getAchievement();
        AchievementsDTO.UploadAchievement uploadAchievement = achievementDTO.getUploadAchievement();

        // 1
        Collection<UUID> personIds = Objects.nonNull(achievement) ?
                getPersonIdsAsUuid(achievement.getPersonId()) : getPersonIdsAsUuid(uploadAchievement.getPersonId());

        AchievementFullInfoDTO result = new AchievementFullInfoDTO();
        List<AchievementDTO> baseAchievement = new ArrayList<>();

        User user = Objects.nonNull(token) ? userRepository.findFirstByAupdId(authService.getTokenPayload(token).getSub())
                .orElseThrow(PortfolioException.notFound(User.class)) : Utils.safeGet(shareLink, ShareLink::getUser);
        // 3
        if (Objects.nonNull(uploadAchievement)) {
            AchievementClickDTO achievementClick = clickHouseRepository.findAchievementByRecordId(uploadAchievement.getRecordId());

            PortfolioException.check(Objects.nonNull(achievementClick), PortfolioException.get439());
            fillBaseAchievementsFromClickHouse(baseAchievement, Collections.singletonList(achievementClick));
            result.setAchievementActivityFormat(crudService.findFirstRefByValue(
                    AchievementActivityFormatRef.class, achievementClick.getFormat()));
            result.setDescription(achievementClick.getDescription());

            if (Objects.nonNull(user)) {
                UserVisibilitySetting setting = visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordUuid(
                        UUID.fromString(uploadAchievement.getPersonId()), user.getId(),
                        SettingSectionType.ACHIEVEMENT.name().toLowerCase(), achievementClick.getRecordId());
                Utils.safeSet(setting, s -> result.setSettingId(s.getId()));
            }
        } else {
            // 4
            PersonAchievement personAchievement = personAchievementRepository.findByPersonIdInAndIdAndIsDelete(
                    personIds, achievement.getAchievementId(), false);

            PortfolioException.check(Objects.nonNull(personAchievement), PortfolioException.get439());
            // 4.1
            fillBaseAchievementsFromPerson(token, baseAchievement, Collections.singletonList(personAchievement));
            result.setDescription(Objects.requireNonNull(personAchievement).getAchievementDescription());
            result.setAchievementActivityFormat(personAchievement.getAchievementActivityFormat());
            // 5
            personAttachmentMetadataRepository.findAllByAchievementIdAndDeletedIs(
                    personAchievement.getId(), false).forEach(x -> {
                NewAchievementDTO.Attachment attachment = new NewAchievementDTO.Attachment();
                attachment.setId(x.getId());
                attachment.setIsDelete(x.isDeleted());
                attachment.setName(x.getName());
                result.getAttachment().add(attachment);
            });

            // 8
            Set<Long> split = Utils.splitToIds(personAchievement.getLinkedAchievementIds());
            if (!split.isEmpty()) {
                // 9
                List<PersonAchievement> achievementList = personAchievementRepository.findAllByIdInAndIsDelete(split, false);
                result.getLinkedAchievements().addAll(AchievementFullInfoDTO.buildLinkedAchievements(achievementList));
            }

            // 10
            Set<String> upAch = Utils.split(personAchievement.getLinkedUploadAchievements());
            if (!upAch.isEmpty()) {
                Collection<AchievementClickDTO> achievements = clickHouseRepository.findAchievementByRecordId(upAch);

                LinkedList<AchievementDTO> achievementDTOS = new LinkedList<>();
                fillBaseAchievementsFromClickHouse(achievementDTOS, achievements);
                result.getLinkedAchievements().addAll(Utils.transform(achievementDTOS,
                        AchievementFullInfoDTO::fromClickAch));
            }

            // 11
            result.setDateFormat(personAchievement.getDateFormat());
            if (Objects.nonNull(user)) {
                UserVisibilitySetting setting = visibilitySettingRepository.findByPersonIdAndUserIdAndSectionAndRecordId(
                        UUID.fromString(achievement.getPersonId()), user.getId(),
                        SettingSectionType.ACHIEVEMENT.name().toLowerCase(), achievement.getAchievementId());
                Utils.safeSet(setting, s -> result.setSettingId(s.getId()));
            }
        }

        Utils.copyNonNullProperties(baseAchievement.iterator().next(), result);
        return result;
    }

    @Transactional
    public void deleteAchievement(Long id, String personId) {
        PersonAchievement personAchievement = personAchievementRepository.findById(id)
                .orElseThrow(() -> PortfolioException.get(PortfolioCodifiedEnum.E455));
        PortfolioException.check(!personAchievement.getIsDelete(), PortfolioException.get(PortfolioCodifiedEnum.E433));

        String personUuid = personAchievement.getPersonId().toString();
        PortfolioException.check(personUuid.equals(personId), PortfolioException.get(PortfolioCodifiedEnum.E433));

        personAchievement.setIsDelete(Boolean.TRUE);
        personAchievementRepository.save(personAchievement);
    }

    @Transactional
    public AchievementFullInfoDTO changeAchievement(NewAchievementDTO changeAchievementDTO, String personId, Long achievementId, String bearer) {
        // 1
        PersonAchievement personAchievement = personAchievementRepository.findByPersonIdInAndIdAndIsDelete(
                Collections.singletonList(UUID.fromString(personId)), achievementId, false);
        PortfolioException.check(Objects.nonNull(personAchievement), PortfolioException.get445());

        // 2
        personAchievement.setAchievementName(changeAchievementDTO.getAchievementName());
        personAchievement.setAchievementDate(changeAchievementDTO.getAchievementDate());
        personAchievement.setPersonAchievementType(
                crudService.findFirstRef(PersonAchievementTypeRef.class, changeAchievementDTO.getAchievementType()));
        personAchievement.setPersonAchievementCategory(
                crudService.findFirstRef(PersonAchievementCategoryRef.class, changeAchievementDTO.getAchievementCategory()));
        personAchievement.setAchievementDescription(changeAchievementDTO.getAchievementDescription());

        if (Objects.nonNull(changeAchievementDTO.getActivityFormat())) {
            personAchievement.setAchievementActivityFormat(
                    crudService.findFirstRef(AchievementActivityFormatRef.class, changeAchievementDTO.getActivityFormat()));
        } else {
            personAchievement.setAchievementActivityFormat(null);
        }
        if (Objects.nonNull(changeAchievementDTO.getActivityCompletionDate())) {
            personAchievement.setActivityCompletionDate(changeAchievementDTO.getActivityCompletionDate().atStartOfDay());
        } else {
            personAchievement.setActivityCompletionDate(null);
        }

        personAchievement.setLinkedAchievementIds(Utils.join(changeAchievementDTO.getLinkedAchievementID()));
        personAchievement.setLinkedUploadAchievements(Utils.join(changeAchievementDTO.getLinkedUploadAchievementID()));

        // 3
        AchievementFullInfoDTO fullInfoDTO = new AchievementFullInfoDTO();
        List<PersonAttachmentMetadata> allByAchievementId = personAttachmentMetadataRepository.findAllByAchievementId(personAchievement.getId());
        allByAchievementId.stream()
                .filter(x -> !changeAchievementDTO.getAttachment()
                        .stream().map(NewAchievementDTO.Attachment::getId)
                        .collect(Collectors.toList()).contains(x.getId()))
                .forEach(x -> {
                    x.setAchievementId(null);
                });

        Optional.ofNullable(changeAchievementDTO.getAttachment()).ifPresent(x -> {
            x.forEach(attachment -> {
                PersonAttachmentMetadata personAttachmentMetadata = personAttachmentMetadataRepository
                        .findById(attachment.getId()).orElseThrow(PortfolioException::get424);
                if (isNull(personAttachmentMetadata.getAchievementId()) ||
                        // нет в СА Objects.isNull(personAttachmentMetadata.getOlympiadId()) ||
                        personAttachmentMetadata.getAchievementId().equals(achievementId)) {
                    Utils.safeSet(attachment.getIsDelete(), personAttachmentMetadata::setDeleted);
                    personAttachmentMetadata.setAchievementId(personAchievement.getId());
                    personAttachmentMetadata.setModificationDate(LocalDateTime.now());//todo from audit annotation
                    personAttachmentMetadataRepository.save(personAttachmentMetadata);
                    NewAchievementDTO.Attachment attach = new NewAchievementDTO.Attachment();
                    attach.setId(personAttachmentMetadata.getId());
                    attach.setIsDelete(personAttachmentMetadata.isDeleted());
                    attach.setName(personAttachmentMetadata.getName());
                    fullInfoDTO.getAttachment().add(attach);
                }
            });
        });

        personAchievement.setDateFormat(changeAchievementDTO.getDateFormat());

        List<AchievementDTO> baseAchievement = new ArrayList<>();
        fillBaseAchievementsFromPerson(bearer, baseAchievement, Collections.singletonList(personAchievement));
        fullInfoDTO.setDescription(personAchievement.getAchievementDescription());
        fullInfoDTO.setAchievementActivityFormat(personAchievement.getAchievementActivityFormat());
        fullInfoDTO.getLinkedAchievements().addAll(
                AchievementFullInfoDTO.buildLinkedAchievements(personAchievementRepository.findAllByIdIn(changeAchievementDTO.getLinkedAchievementID())));
        Utils.copyNonNullProperties(baseAchievement.iterator().next(), fullInfoDTO);
        return fullInfoDTO;
    }

    @Transactional
    public OlympiadFullInfoDTO changeOlympiad(NewOlympiadDTO newOlympiadDTO, String personId, Long olympiadId, String bearer) {
        // 1
        OlympiadFullInfoDTO fullInfoDTO = new OlympiadFullInfoDTO();
        LearnerOlympiad learnerOlympiad = learnerOlympiadRepository.findByPersonIdInAndIdAndIsDelete(
                Collections.singletonList(UUID.fromString(personId)), olympiadId, false);
        PortfolioException.check(Objects.nonNull(learnerOlympiad), PortfolioException.get459());

        // 2
        learnerOlympiad.setName(newOlympiadDTO.getName());
        learnerOlympiad.setOlympiadType(crudService.findFirstRef(OlympiadTypeRef.class, newOlympiadDTO.getOlympiadTypeId()));
        learnerOlympiad.setProfile(newOlympiadDTO.getProfile());
        learnerOlympiad.setOrganizators(Utils.join(newOlympiadDTO.getOrganizators()));
        Optional.ofNullable(newOlympiadDTO.getOlympiadLevelId()).ifPresent(x -> {
            learnerOlympiad.setOlympiadLevel(crudService.findFirstRef(OlympiadLevelRef.class, x));
        });
        learnerOlympiad.setParticipationDate(newOlympiadDTO.getParticipationDate());
        learnerOlympiad.setOlympiadFormat(crudService.findFirstRef(OlympiadFormatRef.class, newOlympiadDTO.getOlympiadFormatId()));
        learnerOlympiad.setOlympiadResult(crudService.findFirstRef(OlympiadResultRef.class, newOlympiadDTO.getOlympiadResultId()));
        learnerOlympiad.setAdditionalResultInfo(newOlympiadDTO.getAdditionalResultInfo());
        // 2.1
        learnerSubjectRepository.deleteAll(learnerOlympiad.getLearnerOlympiadSubjects()
                .stream().filter(x -> !newOlympiadDTO.getSubjectId().contains(x.getSubject().getCode())).collect(Collectors.toSet()));
        Set<LearnerOlympiadSubject> subjects = new HashSet<>();
        newOlympiadDTO.getSubjectId().stream()
                .filter(x -> !learnerOlympiad.getLearnerOlympiadSubjects().stream()
                        .map(y -> y.getSubject().getCode()).collect(Collectors.toSet()).contains(x))
                .forEach(x -> {
                    LearnerOlympiadSubject subject = new LearnerOlympiadSubject();
                    subject.setOlympiadId(learnerOlympiad.getId());
                    subject.setSubject(crudService.findFirstRef(SubjectsRef.class, x));
                    learnerSubjectRepository.save(subject);
                    subjects.add(subject);
                });
        learnerOlympiad.setLearnerOlympiadSubjects(subjects);

        learnerParallelsRepository.deleteAll(learnerOlympiad.getLearnerOlympiadParallels()
                .stream().filter(x -> !newOlympiadDTO.getParallelId().contains(x.getParallels().getCode())).collect(Collectors.toSet()));
        Set<LearnerOlympiadParallels> parallels = new HashSet<>();
        newOlympiadDTO.getParallelId().stream()
                .filter(x -> !learnerOlympiad.getLearnerOlympiadParallels().stream()
                        .map(y -> y.getParallels().getCode()).collect(Collectors.toSet()).contains(x))
                .forEach(x -> {
                    LearnerOlympiadParallels parallel = new LearnerOlympiadParallels();
                    parallel.setOlympiadId(learnerOlympiad.getId());
                    parallel.setParallels(crudService.findFirstRef(ParallelsRef.class, x));
                    learnerParallelsRepository.save(parallel);
                    parallels.add(parallel);
                });
        learnerOlympiad.setLearnerOlympiadParallels(parallels);

        // 3
        List<PersonAttachmentMetadata> allByOlympiadId = personAttachmentMetadataRepository.findAllByOlympiadId(learnerOlympiad.getId());
        allByOlympiadId.stream()
                .filter(x -> !newOlympiadDTO.getAttachment()
                        .stream().map(NewOlympiadDTO.Attachment::getId)
                        .collect(Collectors.toList()).contains(x.getId()))
                .forEach(x -> {
                    x.setOlympiadId(null);
                });
        Optional.ofNullable(newOlympiadDTO.getAttachment()).ifPresent(x -> {
            x.forEach(attachment -> {
                PersonAttachmentMetadata personAttachmentMetadata = personAttachmentMetadataRepository
                        .findById(attachment.getId()).orElseThrow(PortfolioException::get424);
                if (isNull(personAttachmentMetadata.getOlympiadId()) ||
                        // нет в СА Objects.isNull(personAttachmentMetadata.getOlympiadId()) ||
                        personAttachmentMetadata.getOlympiadId().equals(olympiadId)) {
                    Utils.safeSet(attachment.getIsDelete(), personAttachmentMetadata::setDeleted);
                    personAttachmentMetadata.setOlympiadId(learnerOlympiad.getId());
                    personAttachmentMetadata.setModificationDate(LocalDateTime.now());//todo from audit annotation
                    personAttachmentMetadataRepository.save(personAttachmentMetadata);
                    OlympiadFullInfoDTO.Attachment attach = new OlympiadFullInfoDTO.Attachment();
                    attach.setId(personAttachmentMetadata.getId());
                    attach.setIsDelete(personAttachmentMetadata.isDeleted());
                    attach.setName(personAttachmentMetadata.getName());
                    fullInfoDTO.getAttachment().add(attach);
                }
            });
        });
        learnerOlympiadRepository.save(learnerOlympiad);

        extendOlympiadInfo(fullInfoDTO, learnerOlympiad);
        Optional.ofNullable(learnerOlympiad.getDataSource()).ifPresent(x -> {
            fullInfoDTO.setCreator(getCreator(bearer, x.getCode(), learnerOlympiad.getCreatorId().toString()));
        });
        return fullInfoDTO;
    }

    @Transactional
    public OlympiadFullInfoDTO addNewOlympiad(NewOlympiadDTO newOlympiadDTO, String personId, String bearer) {
        OlympiadFullInfoDTO fullInfoDTO = new OlympiadFullInfoDTO();
        // 1
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        // 2 // fixme NPE
        User user = userRepository.findByAupdIdEquals(tokenPayload.getSub()).stream().findFirst().orElse(null);
        // 3
        LearnerOlympiad learnerOlympiad = new LearnerOlympiad();
        learnerOlympiad.setIsDelete(false);
        learnerOlympiad.setPersonId(UUID.fromString(personId));
        learnerOlympiad.setName(newOlympiadDTO.getName());
        learnerOlympiad.setParticipationDate(newOlympiadDTO.getParticipationDate());
        learnerOlympiad.setOlympiadType(crudService.findFirstRef(OlympiadTypeRef.class, newOlympiadDTO.getOlympiadTypeId()));
        learnerOlympiad.setOlympiadFormat(crudService.findFirstRef(OlympiadFormatRef.class, newOlympiadDTO.getOlympiadFormatId()));
        Optional.ofNullable(newOlympiadDTO.getAdditionalResultInfo()).ifPresent(learnerOlympiad::setAdditionalResultInfo);
        learnerOlympiad.setCreationDate(LocalDateTime.now());
        Optional.ofNullable(newOlympiadDTO.getOlympiadLevelId()).ifPresent(x -> {
            learnerOlympiad.setOlympiadLevel(crudService.findFirstRef(OlympiadLevelRef.class, x));
        });
        Optional.ofNullable(newOlympiadDTO.getOlympiadResultId()).ifPresent(x -> {
            learnerOlympiad.setOlympiadResult(crudService.findFirstRef(OlympiadResultRef.class, x));
        });
        learnerOlympiad.setOrganizators(Utils.join(newOlympiadDTO.getOrganizators()));
        Optional.ofNullable(newOlympiadDTO.getProfile()).ifPresent(learnerOlympiad::setProfile);

        CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(
                Objects.requireNonNull(user).getCurrentUserRoles(), CurrentUserRolesDTO.class);

        if (currentUserRolesDTO.getCurrentMeshRoleId().equals(adminId) ||
                currentUserRolesDTO.getCurrentMeshRoleId().equals(employeeId)) {
            Optional.ofNullable(tokenPayload.getStf()).ifPresent(x -> learnerOlympiad.setCreatorId(UUID.fromString(x)));
            learnerOlympiad.setDataSource(crudService.findFirstRefByValue(DataSourceRef.class, "Сотрудник ОО"));
        } else {
            if (currentUserRolesDTO.getCurrentMeshRoleId().equals(parentId)) {
                Optional.ofNullable(tokenPayload.getMsh()).ifPresent(x -> learnerOlympiad.setCreatorId(UUID.fromString(x)));
                learnerOlympiad.setDataSource(crudService.findFirstRefByValue(DataSourceRef.class, "Родитель"));
            } else {
                Optional.ofNullable(tokenPayload.getMsh()).ifPresent(x -> learnerOlympiad.setCreatorId(UUID.fromString(x)));
                learnerOlympiad.setDataSource(crudService.findFirstRefByValue(DataSourceRef.class, "Учащийся"));
            }
        }
        learnerOlympiadRepository.save(learnerOlympiad);
        // 4
        Set<LearnerOlympiadSubject> subjects = new HashSet<>();
        newOlympiadDTO.getSubjectId().forEach(x -> {
            LearnerOlympiadSubject subject = new LearnerOlympiadSubject();
            subject.setOlympiadId(learnerOlympiad.getId());
            subject.setSubject(crudService.findFirstRef(SubjectsRef.class, x));
            learnerSubjectRepository.save(subject);
            subjects.add(subject);
        });
        learnerOlympiad.setLearnerOlympiadSubjects(subjects);

        Set<LearnerOlympiadParallels> parallels = new HashSet<>();
        newOlympiadDTO.getParallelId().forEach(x -> {
            LearnerOlympiadParallels parallel = new LearnerOlympiadParallels();
            parallel.setOlympiadId(learnerOlympiad.getId());
            parallel.setParallels(crudService.findFirstRef(ParallelsRef.class, x));
            learnerParallelsRepository.save(parallel);
            parallels.add(parallel);
        });
        learnerOlympiad.setLearnerOlympiadParallels(parallels);
        learnerOlympiadRepository.save(learnerOlympiad);

        // 5
        Optional.ofNullable(newOlympiadDTO.getAttachment()).ifPresent(x -> {
            x.forEach(attachment -> {
                PersonAttachmentMetadata personAttachmentMetadata = personAttachmentMetadataRepository.findById(attachment.getId()).orElse(null);
                if (Objects.nonNull(personAttachmentMetadata)) {
                    if (isNull(personAttachmentMetadata.getAchievementId()) ||
                            isNull(personAttachmentMetadata.getOlympiadId())) {
                        Optional.ofNullable(attachment.getIsDelete()).ifPresent(personAttachmentMetadata::setDeleted);
                        personAttachmentMetadata.setOlympiadId(learnerOlympiad.getId());
                        personAttachmentMetadataRepository.save(personAttachmentMetadata);
                    } else {
                        throw PortfolioException.get453();
                    }
                }
            });
        });

        extendOlympiadInfo(fullInfoDTO, learnerOlympiad);
        Optional.ofNullable(learnerOlympiad.getDataSource()).ifPresent(x -> {
            fullInfoDTO.setCreator(getCreator(bearer, x.getCode(), learnerOlympiad.getCreatorId().toString()));
        });
        personAttachmentMetadataRepository.findAllByOlympiadId(learnerOlympiad.getId()).forEach(x -> {
            OlympiadFullInfoDTO.Attachment attachment = new OlympiadFullInfoDTO.Attachment();
            attachment.setId(x.getId());
            attachment.setIsDelete(x.isDeleted());
            attachment.setName(x.getName());
            fullInfoDTO.getAttachment().add(attachment);
        });

        return fullInfoDTO;
    }

    @Transactional
    public AchievementFullInfoDTO addNewAchievement(NewAchievementDTO newAchievementDTO, String personId, String bearer) {
        AchievementFullInfoDTO fullInfoDTO = new AchievementFullInfoDTO();
        // 1
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        // 2 // fixme NPE
        User user = userRepository.findByAupdIdEquals(tokenPayload.getSub()).stream().findFirst().orElse(null);
        // 3
        PersonAchievement personAchievement = new PersonAchievement();
        personAchievement.setPersonId(UUID.fromString(personId));
        personAchievement.setIsDelete(Boolean.FALSE);
        personAchievement.setAchievementName(newAchievementDTO.getAchievementName());
        personAchievement.setAchievementDate(newAchievementDTO.getAchievementDate());
        personAchievement.setPersonAchievementType(
                crudService.findFirstRef(PersonAchievementTypeRef.class, newAchievementDTO.getAchievementType()));
        personAchievement.setPersonAchievementCategory(
                crudService.findFirstRef(PersonAchievementCategoryRef.class, newAchievementDTO.getAchievementCategory()));
        personAchievement.setAchievementDescription(newAchievementDTO.getAchievementDescription());
        personAchievement.setCreationDate(LocalDateTime.now());
        Optional.ofNullable(newAchievementDTO.getActivityFormat()).ifPresent(x ->
                personAchievement.setAchievementActivityFormat(crudService.findFirstRef(AchievementActivityFormatRef.class, x)));
        Optional.ofNullable(newAchievementDTO.getActivityCompletionDate()).ifPresent(x ->
                personAchievement.setActivityCompletionDate(x.atStartOfDay()));
        personAchievement.setLinkedAchievementIds(Utils.join(newAchievementDTO.getLinkedAchievementID()));
        personAchievement.setLinkedUploadAchievements(Utils.join(newAchievementDTO.getLinkedUploadAchievementID()));

        CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(
                Objects.requireNonNull(user).getCurrentUserRoles(), CurrentUserRolesDTO.class);

        if (currentUserRolesDTO.getCurrentMeshRoleId().equals(adminId) ||
                currentUserRolesDTO.getCurrentMeshRoleId().equals(employeeId)) {
            personAchievement.setCreatorId(tokenPayload.getStf());
            personAchievement.setDataSource(crudService.findFirstRefByValue(DataSourceRef.class, "Сотрудник ОО"));
        } else {
            if (currentUserRolesDTO.getCurrentMeshRoleId().equals(parentId)) {
                personAchievement.setCreatorId(tokenPayload.getMsh());
                personAchievement.setDataSource(crudService.findFirstRefByValue(DataSourceRef.class, "Родитель"));
            } else {
                personAchievement.setCreatorId(tokenPayload.getMsh());
                personAchievement.setDataSource(crudService.findFirstRefByValue(DataSourceRef.class, "Учащийся"));
            }
        }
        personAchievement.setDateFormat(newAchievementDTO.getDateFormat());
        personAchievementRepository.save(personAchievement);
        // 4
        Optional.ofNullable(newAchievementDTO.getAttachment()).ifPresent(x -> {
            x.forEach(attachment -> {
                PersonAttachmentMetadata personAttachmentMetadata = personAttachmentMetadataRepository.findById(attachment.getId()).orElse(null);
                if (Objects.nonNull(personAttachmentMetadata)) {
                    if (isNull(personAttachmentMetadata.getAchievementId()) ||
                            isNull(personAttachmentMetadata.getOlympiadId())) {
                        Optional.ofNullable(attachment.getIsDelete()).ifPresent(personAttachmentMetadata::setDeleted);
                        personAttachmentMetadata.setAchievementId(personAchievement.getId());
                        personAttachmentMetadataRepository.save(personAttachmentMetadata);
                    } else {
                        throw PortfolioException.get424();
                    }
                }
            });
        });
        personAttachmentMetadataRepository.findAllByAchievementId(personAchievement.getId()).forEach(x -> {
            NewAchievementDTO.Attachment attachment = new NewAchievementDTO.Attachment();
            attachment.setId(x.getId());
            attachment.setIsDelete(x.isDeleted());
            attachment.setName(x.getName());
            fullInfoDTO.getAttachment().add(attachment);
        });

        List<AchievementDTO> baseAchievement = new ArrayList<>();
        fillBaseAchievementsFromPerson(bearer, baseAchievement, Collections.singletonList(personAchievement));
        fullInfoDTO.setDescription(personAchievement.getAchievementDescription());
        fullInfoDTO.setAchievementActivityFormat(personAchievement.getAchievementActivityFormat());
        Utils.safeGet(newAchievementDTO.getLinkedAchievementID(), a -> fullInfoDTO.getLinkedAchievements()
                .addAll(AchievementFullInfoDTO.buildLinkedAchievements(personAchievementRepository.findAllByIdIn(a))));
        Utils.copyNonNullProperties(baseAchievement.iterator().next(), fullInfoDTO);
        return fullInfoDTO;
    }

    public List<AchievementDTO> getAchievements(String id, String token, String share) {
        ShareLink shareLink = null;
        if (Objects.nonNull(share)) {
            shareLink = parseCookie(share);
            //PortfolioException.check(Objects.isNull(shareLink) || shareLink.getAchievements(), PortfolioException.get461());
        }

        List<Long> hiddenIds = new LinkedList<>();
        List<UUID> hiddenUUIDs = new LinkedList<>();
        Map<Object, Long> visibleSettings = new HashMap<>();

        User user = Objects.nonNull(token) ? userRepository.findFirstByAupdId(authService.getTokenPayload(token).getSub()).orElse(null)
                : Utils.safeGet(shareLink, ShareLink::getUser);
        if (Objects.nonNull(user)) {
            List<UserVisibilitySetting> hiddenAchievements = visibilitySettingRepository.findAllByPersonIdAndUserIdAndSection(UUID.fromString(id), user.getId(),
                    SettingSectionType.ACHIEVEMENT.name().toLowerCase());
            hiddenAchievements.forEach(a -> {
                        if (isNull(a.getRecordId())) {
                            hiddenUUIDs.add(a.getRecordUuid());
                            visibleSettings.put(a.getRecordUuid(), a.getId());
                        } else {
                            hiddenIds.add(a.getRecordId());
                            visibleSettings.put(a.getRecordId(), a.getId());
                        }
                    }
            );
        }

        List<AchievementDTO> result = new LinkedList<>();
        // 2
        List<UUID> personIdsAsUuid = getPersonIdsAsUuid(id);
        // 4
        List<PersonAchievement> achievements = Objects.nonNull(share) ? personAchievementRepository
                .findAllByPersonIdInAndIsDeleteAndIdNotIn(personIdsAsUuid, false, hiddenIds) :
                personAchievementRepository.findAllByPersonIdInAndIsDelete(personIdsAsUuid, false);
        fillBaseAchievementsFromPerson(token, false, result, achievements);

        // 5
        fillCreator(token, result);
        // 3
        List<AchievementClickDTO> achievementClick = clickHouseRepository.getAchievements(
                personIdsAsUuid.stream().map(UUID::toString).collect(Collectors.toList()),
                Objects.nonNull(share) ? hiddenUUIDs : Collections.emptyList());
        fillBaseAchievementsFromClickHouse(result, achievementClick);

        result.forEach(a -> a.setSettingId(visibleSettings.getOrDefault(isNull(a.getId()) ? a.getRecordId() : a.getId(), null)));

        Comparator<AchievementDTO> comparator = (o1, o2) -> ObjectUtils.compare(o2.getDate(), o1.getDate(), true);
        return result.stream().sorted(comparator).collect(Collectors.toList());
    }

    /**
     * Получение данных о самодиагностике
     *
     * @param auth
     * @param personId
     * @param period
     * @param subject
     * @return
     */
    public SelfDiagnosticWorkDTO getSelfDiagnostic(String auth, String personId, Integer period, String subject, String share) {

        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            PortfolioException.check(isNull(shareLink) ||
                    (shareLink.getDiagnostic() && shareLink.getStudies()), PortfolioException.get461());
        }

        PortfolioException.check(Objects.nonNull(personId) && (period == 1 || period == 2), PortfolioException.get433());

        //1
        Collection<String> personIds = getPersonIds(personId);
        //2
        SelfDiagnosticWorkDTO selfDiagnosticWorkDTO = new SelfDiagnosticWorkDTO();
        // 3
        if (period == 1) {
            //3
            LocalDate[] diagnosticPeriod = getDiagnosticPeriod(period);
            //4
            List<SelfDiagnosticClickDTO> selfDiagnostics = clickHouseRepository.findSelfDiagnostics(
                    personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1,
                    diagnosticPeriod[0], diagnosticPeriod[1], subject);

            if (selfDiagnostics.isEmpty()) {
                selfDiagnosticWorkDTO.setTotal(0);
                return selfDiagnosticWorkDTO;
            }

            //4.1
            selfDiagnosticWorkDTO.setTotal(selfDiagnostics.size());
            //4.2
            makeGroupMarks(selfDiagnostics, selfDiagnosticWorkDTO);
            //4.3
            makeGroupDuration(selfDiagnostics, selfDiagnosticWorkDTO);
            //4.4.1
            calculateCountUniqSubjects(selfDiagnostics, selfDiagnosticWorkDTO);
            //4.4.2
            calculateCountUniqTypes(selfDiagnostics, selfDiagnosticWorkDTO);
            //4.5
            LocalDate[] analogPeriod = getDiagnosticPeriod(3);
            List<SelfDiagnosticClickDTO> analogDiagnostics = clickHouseRepository.findSelfDiagnostics(
                    personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1, analogPeriod[0], analogPeriod[1], subject);
            if (!analogDiagnostics.isEmpty()) {
                double totalResult = (Double.valueOf(selfDiagnosticWorkDTO.getTotal()) / analogDiagnostics.size()) * 100 - 100;
                selfDiagnosticWorkDTO.setTotalResult(BigDecimal.valueOf(totalResult).setScale(1, RoundingMode.HALF_UP).doubleValue());
            }
            //4.6
            selfDiagnosticWorkDTO.setAverageResult(calculateAvgPercentExecution(selfDiagnostics));
            //4.6.1
            if (!analogDiagnostics.isEmpty()) {
                Double[] earnedAverageScore = {0.0};
                Double maxAverageScore = (double) analogDiagnostics.size();
                analogDiagnostics.forEach(d -> earnedAverageScore[0] += d.getPercentExecutionResult());
                selfDiagnosticWorkDTO.setAverageResultDynamic(Double.valueOf(Math.ceil(selfDiagnosticWorkDTO.getAverageResult() -
                        (earnedAverageScore[0] / maxAverageScore))).intValue());
            }
            //4.7
            selfDiagnosticWorkDTO.setDurationResult(calculateAvgDurationExecution(selfDiagnostics));

            //4.7.1
            Integer[] analogAverageDuration = {0};
            analogDiagnostics.stream().filter(s -> Objects.nonNull(s.getFactExecutionDuration())).forEach(s -> {
                if (s.getPlanExecutionDuration() > 0)
                    analogAverageDuration[0] += ((Double) ((s.getFactExecutionDuration().doubleValue() / s.getPlanExecutionDuration()) * 100)).intValue();
            });
            selfDiagnosticWorkDTO.setDurationResultDynamic(analogAverageDuration[0] / selfDiagnosticWorkDTO.getTotal() - 100);
            //4.8
            selfDiagnosticWorkDTO.setBestResult(clickHouseRepository.findBestResult(
                    personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1, diagnosticPeriod[0], diagnosticPeriod[1]));
        } else {
            //5
            List<SelfDiagnosticClickDTO> selfDiagnostics = clickHouseRepository.findSelfDiagnostics(
                    personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1, null, null, subject);
            //5.1
            selfDiagnosticWorkDTO.setTotal(selfDiagnostics.size());
            //5.2
            makeGroupMarks(selfDiagnostics, selfDiagnosticWorkDTO);
            //5.3
            makeGroupDuration(selfDiagnostics, selfDiagnosticWorkDTO);
            //5.4
            calculateCountUniqSubjects(selfDiagnostics, selfDiagnosticWorkDTO);
            calculateCountUniqTypes(selfDiagnostics, selfDiagnosticWorkDTO);
            //5.5
            if (!selfDiagnostics.isEmpty())
                selfDiagnosticWorkDTO.setEventDate(selfDiagnostics.get(0).getEventDate());
            //5.6
            selfDiagnosticWorkDTO.setAverageResult(calculateAvgPercentExecution(selfDiagnostics));
            //5.7
            selfDiagnosticWorkDTO.setDurationResult(calculateAvgDurationExecution(selfDiagnostics));
            //5.8
            selfDiagnosticWorkDTO.setBestResult(clickHouseRepository.findBestResult(
                    personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1, null, null));
        }

        return selfDiagnosticWorkDTO;
    }


    /**
     * Формирование расчэтного периода для самодиагностики.
     *
     * @param periodVal
     * @return
     */
    private LocalDate[] getDiagnosticPeriod(Integer periodVal) {
        LocalDate[] result = new LocalDate[2];
        //текущий учебный год
        if (periodVal == 1) {
            int year = Calendar.getInstance().get(Calendar.YEAR);
            int month = Calendar.getInstance().get(Calendar.MONTH);
            //если текущая дата до сентября, то начало учебного года было в прошлом году, если после сентября, то в этом
            result[0] = LocalDate.of(month > 8 && month < 12 ? year : year - 1, Month.SEPTEMBER, 1);
            result[1] = LocalDate.now();
        } else
            //аналогичный период прошлого учебного года, специальное значение
            if (periodVal == 3) {
                int year = Calendar.getInstance().get(Calendar.YEAR);
                int month = Calendar.getInstance().get(Calendar.MONTH);
                //если текущая дата до сентября, то прошлый учебный год начался в позапрошлом году
                year = month > 9 ? year - 3 : year - 2;
                result[0] = LocalDate.of(year, Month.SEPTEMBER, 1);
                result[1] = LocalDate.of(year + 1, LocalDate.now().getMonth(), LocalDate.now().getDayOfMonth());
            }
        return result;
    }

    /**
     * Группировка данных по самодиагностике по проценту выполненности работ
     *
     * @param diagnostics
     * @param selfDiagnosticWorkDTO
     */
    public void makeGroupMarks(List<SelfDiagnosticClickDTO> diagnostics, SelfDiagnosticWorkDTO selfDiagnosticWorkDTO) {
        selfDiagnosticWorkDTO.getGroupMark().setFromData(Utils.transform(diagnostics, SelfDiagnosticClickDTO::getPercentExecutionResult));
    }

    /**
     * Группировка даннх о самодиагностике по времени выполнения
     *
     * @param diagnostics
     * @param selfDiagnosticWorkDTO
     */
    public void makeGroupDuration(List<SelfDiagnosticClickDTO> diagnostics, SelfDiagnosticWorkDTO selfDiagnosticWorkDTO) {
        SelfDiagnosticWorkDTO.GroupDuration groupDuration = selfDiagnosticWorkDTO.getGroupDuration();

        groupDuration.setDuration0to5(((Long) diagnostics.stream()
                .map(SelfDiagnosticClickDTO::getFactExecutionDuration)
                .filter(Objects::nonNull)
                .filter(d -> d <= 300)
                .count()).intValue());
        groupDuration.setDuration6to10(((Long) diagnostics.stream()
                .map(SelfDiagnosticClickDTO::getFactExecutionDuration)
                .filter(Objects::nonNull)
                .filter(d -> d > 300 && d <= 600)
                .count()).intValue());
        groupDuration.setDuration11to15(((Long) diagnostics.stream()
                .map(SelfDiagnosticClickDTO::getFactExecutionDuration)
                .filter(Objects::nonNull)
                .filter(d -> d > 600 && d <= 900)
                .count()).intValue());
        groupDuration.setDuration16to20(((Long) diagnostics.stream()
                .map(SelfDiagnosticClickDTO::getFactExecutionDuration)
                .filter(Objects::nonNull)
                .filter(d -> d > 900 && d <= 1200)
                .count()).intValue());
        groupDuration.setDurationOver20(((Long) diagnostics.stream()
                .map(SelfDiagnosticClickDTO::getFactExecutionDuration)
                .filter(Objects::nonNull)
                .filter(d -> d > 1200)
                .count()).intValue());

        groupDuration.setDurationUnknow(Math.toIntExact(diagnostics.stream()
                .map(SelfDiagnosticClickDTO::getFactExecutionDuration)
                .filter(Objects::isNull)
                .count()));
    }

    /**
     * @param diagnostics
     * @param selfDiagnosticWorkDTO
     */
    public void calculateCountUniqSubjects(List<SelfDiagnosticClickDTO> diagnostics, SelfDiagnosticWorkDTO selfDiagnosticWorkDTO) {
        // 4.4.1
        Map<String, Long> subj2Count = diagnostics.stream().collect(Collectors.groupingBy(
                SelfDiagnosticClickDTO::getSubjectName, Collectors.counting()));
        selfDiagnosticWorkDTO.setSelfDiagnosticSubjects(Utils.transform(subj2Count.entrySet(),
                e -> new SelfDiagnosticWorkDTO.SelfDiagnosticSubject(e.getKey(), e.getValue())));
    }

    public void calculateCountUniqTypes(List<SelfDiagnosticClickDTO> diagnostics, SelfDiagnosticWorkDTO selfDiagnosticWorkDTO) {
        // 4.4.2
        Map<String, Long> subj2Count = diagnostics.stream().collect(Collectors.groupingBy(
                SelfDiagnosticClickDTO::getWorkType, Collectors.counting()));
        selfDiagnosticWorkDTO.setSelfDiagnosticType(Utils.transform(subj2Count.entrySet(),
                e -> new SelfDiagnosticWorkDTO.SelfDiagnosticType(e.getKey(), e.getValue())));
    }

    public Integer calculateAvgPercentExecution(List<SelfDiagnosticClickDTO> selfDiagnostics) {
        if (selfDiagnostics.isEmpty()) return 0;
        Double[] earnedScore = {0.0};
        selfDiagnostics.forEach(d -> earnedScore[0] += d.getPercentExecutionResult());
        return ((Long) Math.round(earnedScore[0] / selfDiagnostics.size())).intValue();
    }

    public Integer calculateAvgDurationExecution(List<SelfDiagnosticClickDTO> selfDiagnostics) {
        if (selfDiagnostics.isEmpty()) return 0;
        Integer[] averageDurationSum = {0};
        selfDiagnostics.stream().filter(s -> Objects.nonNull(s.getFactExecutionDuration())).forEach(s -> {
            if (s.getPlanExecutionDuration() > 0)
                averageDurationSum[0] += ((Double) ((s.getFactExecutionDuration().doubleValue() / s.getPlanExecutionDuration()) * 100)).intValue();
        });
        return averageDurationSum[0] / selfDiagnostics.size();
    }

    public List<String> getDiagnosticSubjects(String auth, String personId, String share) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            /*PortfolioException.check(Objects.isNull(shareLink) ||
                    (shareLink.getSelfTraining() && shareLink.getSelfDiagnostic()), PortfolioException.get461());*/
        }
        //2
        Collection<String> personIds = getPersonIds(personId);
        //3
        return clickHouseRepository.findSelfDiagnosticSubjects(personIds, EventType.SELF_DIAGNOSTIC.getCode(), 1);
    }

    private void fillCreator(@Nullable String token, List<AchievementDTO> result) {
        Set<String> parentCreator = Sets.newHashSet();
        Set<String> staffCreator = Sets.newHashSet();
//        Integer staffId = 12, parent = 11;
        DataSourceRef parent = crudService.findFirstRefByValue(DataSourceRef.class, "Родитель");
        DataSourceRef employee = crudService.findFirstRefByValue(DataSourceRef.class, "Сотрудник ОО");
        // 5
        AccessTokenPayloadDto tokenPayload = Utils.safeGet(token, authService::getTokenPayload);
        for (AchievementDTO achievement : result) {
            String creatorId = achievement.getCreatorId();
            if (isNull(creatorId)) continue;
            CreatorDTO creator = getCreator(tokenPayload, creatorId);
            achievement.setCreator(creator);

            if (!creator.getCurrentUserIsCreator()) {
                Integer code = achievement.getDataSource();
                if (employee.getCode().equals(code)) {
                    staffCreator.add(creatorId);
                }
                if (parent.getCode().equals(code)) {
                    parentCreator.add(creatorId);
                }
            }
        }
//        if (Objects.isNull(token)) return;
        Map<String, UserContextDTO.Info.UserFio> emptyMap = Collections.emptyMap();

        // 5.1.1
        Map<String, UserContextDTO.Info.UserFio> staffMap = safetyGet(emptyMap, () ->
                nsiService.getEmployee(staffCreator).stream().collect(Collectors
                        .toMap(NsiDTO.Response::getFirstName, UserContextDTO.Info.UserFio::build)));
        // 5.1.2
        Map<String, UserContextDTO.Info.UserFio> parentMap = safetyGet(emptyMap, () ->
                contingentService.getPersons(parentCreator).stream().collect(Collectors
                        .toMap(p -> p.getPersonId().toString(), UserContextDTO.Info.UserFio::build)));

        if (staffMap.isEmpty() && parentMap.isEmpty()) return;

        for (AchievementDTO achievementDTO : result) {
            CreatorDTO creator = achievementDTO.getCreator();

            if (!creator.getCurrentUserIsCreator()) {
                Integer code = achievementDTO.getDataSource();
                if (employee.getCode().equals(code)) {
                    creator.setUserFio(staffMap.get(creator.getCreatorId()));
                }
                if (parent.getCode().equals(code)) {
                    creator.setUserFio(parentMap.get(creator.getCreatorId()));
                }
            }
        }
    }

    private void fillOlympiadCreator(@Nullable String token, List<OlympiadDTO> result) {
        Set<String> parentCreator = Sets.newHashSet();
        Set<String> staffCreator = Sets.newHashSet();

        DataSourceRef parent = crudService.findFirstRefByValue(DataSourceRef.class, "Родитель");
        DataSourceRef employee = crudService.findFirstRefByValue(DataSourceRef.class, "Сотрудник ОО");

        // 5
        AccessTokenPayloadDto tokenPayload = Utils.safeGet(token, authService::getTokenPayload);
        for (OlympiadDTO olympiad : result) {
            String creatorId = olympiad.getCreatorId();
            if (isNull(creatorId)) continue;
            CreatorDTO creator = getCreator(tokenPayload, creatorId);
            olympiad.setCreator(creator);

            if (!creator.getCurrentUserIsCreator()) {
                Integer code = olympiad.getDataSource();
                if (employee.getCode().equals(code)) {
                    staffCreator.add(creatorId);
                }
                if (parent.getCode().equals(code)) {
                    parentCreator.add(creatorId);
                }
            }
        }
//        if (Objects.isNull(token)) return;
        Map<String, UserContextDTO.Info.UserFio> emptyMap = Collections.emptyMap();

        // 5.1.1
        Map<String, UserContextDTO.Info.UserFio> staffMap = safetyGet(emptyMap, () ->
                nsiService.getEmployee(staffCreator).stream().collect(Collectors
                        .toMap(NsiDTO.Response::getFirstName, UserContextDTO.Info.UserFio::build)));
        // 5.1.2
        Map<String, UserContextDTO.Info.UserFio> parentMap = safetyGet(emptyMap, () ->
                contingentService.getPersons(parentCreator).stream().collect(Collectors
                        .toMap(p -> p.getPersonId().toString(), UserContextDTO.Info.UserFio::build)));

        if (staffMap.isEmpty() && parentMap.isEmpty()) return;

        for (OlympiadDTO olympiad : result) {
            CreatorDTO creator = olympiad.getCreator();

            if (!creator.getCurrentUserIsCreator()) {
                Integer code = olympiad.getDataSource();
                if (employee.getCode().equals(code)) {
                    creator.setUserFio(staffMap.get(creator.getCreatorId()));
                }
                if (parent.getCode().equals(code)) {
                    creator.setUserFio(parentMap.get(creator.getCreatorId()));
                }
            }
        }
    }

    private void fillBaseAchievementsFromPerson(String token,
                                                List<AchievementDTO> result,
                                                List<PersonAchievement> achievements) {
        fillBaseAchievementsFromPerson(token, true, result, achievements);
    }

    private void fillBaseAchievementsFromPerson(String token, boolean withClassicCreatorGetter,
                                                List<AchievementDTO> result, List<PersonAchievement> achievements) {
        for (PersonAchievement achievement : achievements) {
            AchievementDTO arAchievement = new AchievementDTO();

            arAchievement.setId(achievement.getId());
            arAchievement.setName(achievement.getAchievementName());
            arAchievement.setCreationDate(achievement.getCreationDate());
            arAchievement.setDate(achievement.getAchievementDate());
            LocalDate activityCompletionDate = safeGet(achievement.getActivityCompletionDate(), LocalDateTime::toLocalDate);
            arAchievement.setActivityCompletionDate(activityCompletionDate);
            arAchievement.setSource(achievement.getDataSource());
            arAchievement.setType(achievement.getPersonAchievementType());
            arAchievement.setCategory(achievement.getPersonAchievementCategory());

            // 5.2
            arAchievement.setEvidence(StringUtils.isNotEmpty(achievement.getAchievementFileReferences()));

            Integer sourceId = safeGet(arAchievement.getSource(), AbstractRefEntity::getCode);
            if (withClassicCreatorGetter) {
                arAchievement.setCreator(getCreator(token, sourceId, achievement.getCreatorId()));
            } else {
                arAchievement.setCreatorId(achievement.getCreatorId());
                arAchievement.setDataSource(sourceId);
            }
            arAchievement.setEmployeeCanEdit(achievement.getEmployeeCanEdit());
            arAchievement.setDateFormat(achievement.getDateFormat());

            result.add(arAchievement);
        }
    }

    private void fillBaseAchievementsFromClickHouse(List<AchievementDTO> result, Collection<AchievementClickDTO> achievementClick) {
        for (AchievementClickDTO achievementClickDTO : achievementClick) {
            AchievementDTO achievement = new AchievementDTO();

            achievement.setName(achievementClickDTO.getName());
            achievement.setDate(achievementClickDTO.getEventDate());
            achievement.setCreationDate(LocalDateTime.of(achievementClickDTO.getEventDate(), LocalTime.MIN));
            achievement.setActivityCompletionDate(achievementClickDTO.getActivityDate());

            achievement.setDateTime(achievementClickDTO.getEventTime());

            achievement.setType(crudService.findFirstRefByValue(
                    PersonAchievementTypeRef.class, achievementClickDTO.getType()));
            achievement.setCategory(crudService.findFirstRefByValue(
                    PersonAchievementCategoryRef.class, achievementClickDTO.getCategory()));
            achievement.setSource(crudService.findFirstRefByValue(
                    DataSourceRef.class, achievementClickDTO.getSource()));
            achievement.setAchievementActivityFormat(crudService.findFirstRefByValue(
                    AchievementActivityFormatRef.class, achievementClickDTO.getFormat()));
            achievement.setRecordId(achievementClickDTO.getRecordId());

            result.add(achievement);
        }
    }

    private CreatorDTO getCreator(@Nullable String token, Integer sourceId, String creatorId) {

        // 5.1 // fixme foreach parse
        AccessTokenPayloadDto tokenPayload = Utils.safeGet(token, authService::getTokenPayload);
        CreatorDTO creator = getCreator(tokenPayload, creatorId);
        DataSourceRef parent = crudService.findFirstRefByValue(DataSourceRef.class, "Родитель");
        DataSourceRef employee = crudService.findFirstRefByValue(DataSourceRef.class, "Сотрудник ОО");
        // fixme foreach get
        creator.setUserFio(getUserFio(employee.getCode(), parent.getCode(), sourceId, creatorId));
        return creator;
    }

    private CreatorDTO getCreator(@Nullable AccessTokenPayloadDto tokenPayload, String creatorId) {
        CreatorDTO creator = new CreatorDTO();
        creator.setCreatorId(creatorId);

        Boolean currentUserIsCreator = safeGet(tokenPayload, t ->
                creatorId.equals(t.getMsh()) || creatorId.equals(t.getStf()));
        creator.setCurrentUserIsCreator(BooleanUtils.toBoolean(currentUserIsCreator));
        return creator;
    }

    private UserContextDTO.Info.UserFio getUserFio(Integer staff, Integer parent, Integer sourceId, String creatorId) {
        // 5
        if (staff.equals(sourceId)) {
            return safetyGet(() -> nsiService
                    .getEmployee(creatorId)
                    .map(UserContextDTO.Info.UserFio::build)
                    .orElse(null));
        }
        if (parent.equals(sourceId)) {
            return safetyGet(() -> contingentService
                    .getPersonByFilter(creatorId)
                    .map(UserContextDTO.Info.UserFio::build)
                    .orElse(null));
        }
        return null;
    }

    @Nullable
    @SneakyThrows
    public ShareLink parseCookie(String share) {
        return shareLinkUtils.parseCookie(share);
    }

    public List<CulturalInstitutionClickDTO> getVisitCulturalInstitutions(String personId, String share) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            if (isNull(shareLink) || !shareLink.getCulture()) {
                return new ArrayList<>();
            }
        }
        List<CulturalInstitutionClickDTO> culturalInstitutions = clickHouseRepository.findCulturalInstitutions(personId);
        // ClickHouse хранит время в GTC +0
        culturalInstitutions.forEach(x -> x.setVisitTime(x.getVisitTime().plusHours(3)));
        return culturalInstitutions;
    }

    //А_ВН_11
    public <T extends PersonallyEntity> List<T> getAchievementsFromClickhouse(String personId, Integer achievementType, String share) {
        Map<String, Object> searchMap = new HashMap<>();
        List<Integer> typeCodes = new ArrayList<>();
        List<Integer> achievementCodes = new ArrayList<>();
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            PortfolioException.check(!(!shareLink.getStudies() && !shareLink.getScience() && !shareLink.getSport()
                    && !shareLink.getCreation() && !shareLink.getCulture() && !shareLink.getCivil()
                    && !shareLink.getProfession()), PortfolioException.get461());
//            validateShareLink(shareLink, achievementType);
            fillCodeLists(shareLink, typeCodes, achievementCodes, achievementType);
        }

        Collection<String> personIds = getPersonIds(personId);
        Pattern p = Pattern.compile("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$");
        if (!p.matcher(personId).matches()) {
            throw PortfolioException.get464();
        }

        List<AchievementClickDTO> achievements;
        searchMap.put("personId", personId);
        switch (achievementType) {
            case (1):
                List<Employment> employments;
                if (typeCodes.isEmpty()) {
                    employments = crudService.findAll(Employment.class, searchMap);
                } else {
                    employments = emplRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                employments.forEach(x -> x.reachTransient(crudService));
                List<EmploymentDTO> employmentDTOList = mapToEmploymentDto(employments);
                fillLinkedObject(employmentDTOList);
                achievements = clickHouseRepository.getAchievements(personIds,
                        EMPLOYMENT.getAchievementType(), achievementCodes);
                employmentDTOList.addAll(mapToEmployment(achievements, personId));
                employmentDTOList.sort(nullsLast(comparing(x -> nonNull(x.getEndDate()) ? x.getEndDate() : x.getStartDate(), nullsLast(reverseOrder()))));
                return (List<T>) employmentDTOList;
            case (2):
                List<Event> events;
                if (typeCodes.isEmpty()) {
                    events = crudService.findAll(Event.class, searchMap);
                } else {
                    events = eventRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                Map<Long, List<OlympiadOutcome>> olympiadOutcomeMap = olympiadOutcomeRepository
                        .findAllByEventIdInAndDelete(events, Boolean.FALSE)
                        .stream()
                        .collect(Collectors.groupingBy(olympiadOutcome -> olympiadOutcome.getEventId().getId()));
                events.forEach(x -> x.reachTransient(crudService));
                List<EventDTO> eventDTOList = mapToEventDtoWithOlympiadResult(events, olympiadOutcomeMap);
                fillLinkedObject(eventDTOList);
                /*achievements = clickHouseRepository.getAchievements(personIds,
                        EVENT.getAchievementType(), achievementCodes);
                eventDTOList.addAll(mapToEvents(achievements, personId));*/
                /*if (isNull(share) || typeCodes.contains(8)) {
                    eventDTOList.addAll(getOlympiadsFromClickhouse(personId, personIds));
                }*/
                eventDTOList.sort(nullsLast(comparing(x -> nonNull(x.getEndDate()) ? x.getEndDate() : x.getStartDate(), nullsLast(reverseOrder()))));
                return (List<T>) eventDTOList;
            case (3):
                List<Reward> rewards;
                if (typeCodes.isEmpty()) {
                    rewards = crudService.findAll(Reward.class, searchMap);
                } else {
                    rewards = rewardRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                rewards.forEach(x -> x.reachTransient(crudService));
                List<RewardDTO> rewardDTOList = mapToRewardDto(rewards);
                fillLinkedObject(rewardDTOList);
                if (isNull(share)) achievementCodes.addAll(Arrays.asList(0, 2, 3, 4, 5, 6, 7));
                achievements = clickHouseRepository.getAchievements(personIds,
                        REWARD.getAchievementType(), achievementCodes);
                rewardDTOList.addAll(mapToReward(achievements, personId));
                rewardDTOList.sort(nullsLast(comparing(RewardDTO::getDate, nullsLast(reverseOrder()))));
                return (List<T>) rewardDTOList;
            case (5):
                List<SportReward> sportRewards;
                if (typeCodes.isEmpty()) {
                    sportRewards = crudService.findAll(SportReward.class, searchMap);
                } else {
                    sportRewards = sportRewardRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                sportRewards.forEach(x -> x.reachTransient(crudService));
                List<SportRewardDTO> sportRewardDTOList = mapToSportRewardDto(sportRewards);
                fillLinkedObject(sportRewardDTOList);
                if (isNull(share)) achievementCodes.add(1);
                achievements = clickHouseRepository.getAchievements(personIds,
                        PersonallyEntityEnum.SPORT_REWARD.getAchievementType(), achievementCodes);
                sportRewardDTOList.addAll(mapToSportReward(achievements, personId));
                sportRewardDTOList.sort(nullsLast(comparing(SportReward::getDate, nullsLast(reverseOrder()))));
                return (List<T>) sportRewardDTOList;
            case (6):
                List<Affilation> affilations;
                if (typeCodes.isEmpty()) {
                    affilations = crudService.findAll(Affilation.class, searchMap);
                } else {
                    affilations = affRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                fillLinkedObject(affilations);
                affilations.forEach(x -> x.reachTransient(crudService));
                affilations.sort(nullsLast(comparing(x -> nonNull(x.getEndDate()) ? x.getEndDate() : x.getStartDate(), nullsLast(reverseOrder()))));
                return (List<T>) affilations;
            case (4):
                List<Project> projects;
                if (typeCodes.isEmpty()) {
                    projects = crudService.findAll(Project.class, searchMap);
                } else {
                    projects = projectRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                projects.forEach(x -> x.reachTransient(crudService));
                List<ProjectDTO> projectDTOList = mapToProjectDto(projects);
                fillLinkedObject(projectDTOList);
                achievements = clickHouseRepository.getAchievements(personIds,
                        PersonallyEntityEnum.PROJECT.getAchievementType(), achievementCodes);
                projectDTOList.addAll(mapToProject(achievements, personId));
                projectDTOList.sort(nullsLast(comparing(x -> nonNull(x.getEndDate()) ? x.getEndDate() : x.getStartDate(), nullsLast(reverseOrder()))));
                return (List<T>) projectDTOList;
            case (7):
                List<GIAWorldskills> giaWorldskills;
                if (typeCodes.isEmpty()) {
                    giaWorldskills = crudService.findAll(GIAWorldskills.class, searchMap);
                } else {
                    giaWorldskills = giaWorldskillsRepository.findAllByPersonIdAndTypeCodeIn(personId, typeCodes);
                }
                fillLinkedObject(giaWorldskills);
                giaWorldskills.forEach(x -> x.reachTransient(crudService));
                giaWorldskills.sort(nullsLast(comparing(GIAWorldskills::getResultDate, nullsLast(reverseOrder()))));
                return (List<T>) giaWorldskills;
        }
        return new ArrayList<>();
    }

//    public List<StandardExamDTO> searchGovexamsByString(String personId, String searchString) {
//        Collection<String> personIds = getPersonIds(personId);
//        List<StandardExamDTO> achievements = clickHouseRepository.searchGovexams(personIds, searchString);
//        return achievements;
//    }

    public <T extends Linkable> void fillLinkedObject(List<T> entities) {
        List<Long> linkedIds = entities.stream()
                .map(Linkable::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (linkedIds.isEmpty()) {
            return;
        }
        List<LinkedObject> linkedObjects = linkedObjectRepository.findAllByEntityIdIn(linkedIds);
        for (LinkedObject linkedObject : linkedObjects) {
            Linkable entity1 = entities.stream()
                    .filter(x -> Objects.nonNull(x.getId()))
                    .filter(x -> x.getId().equals(linkedObject.getEntityId1()))
                    .findFirst().orElse(null);
            if (nonNull(entity1)) {
                LinkedObjectDTO dto = new LinkedObjectDTO();
                dto.setEntityId(linkedObject.getEntityId2());
                dto.setEntityType(linkedObject.getEntityType2());
                if (nonNull(entity1.getLinkedObjects())) {
                    entity1.getLinkedObjects().add(dto);
                } else {
                    List<LinkedObjectDTO> linkedObjectList = new ArrayList<>();
                    linkedObjectList.add(dto);
                    entity1.setLinkedObjects(linkedObjectList);
                }
            }
            Linkable entity2 = entities.stream()
                    .filter(x -> Objects.nonNull(x.getId()))
                    .filter(x -> x.getId().equals(linkedObject.getEntityId2()))
                    .findFirst().orElse(null);
            if (nonNull(entity2)) {
                LinkedObjectDTO dto = new LinkedObjectDTO();
                dto.setEntityId(linkedObject.getEntityId1());
                dto.setEntityType(linkedObject.getEntityType1());
                if (nonNull(entity2.getLinkedObjects())) {
                    entity2.getLinkedObjects().add(dto);
                } else {
                    List<LinkedObjectDTO> linkedObjectList = new ArrayList<>();
                    linkedObjectList.add(dto);
                    entity2.setLinkedObjects(linkedObjectList);
                }
            }
        }
    }

    public GetIndependentDiagnosticsResponse searchIndependentDiagnostic(String personId, Integer period, String subject,
                                                                         String share, Integer count) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            PortfolioException.check(Objects.nonNull(shareLink) &&
                    shareLink.getDiagnostic() && shareLink.getStudies(), PortfolioException.get461());
        }

        if (!(period.equals(1) || period.equals(2))) throw PortfolioException.get433();
        LocalDate start = null;
        LocalDate end = null;
        if (period.equals(1)) {
            LocalDate[] dates = getDiagnosticPeriod(period);
            start = dates[0];
            end = dates[1];
        }

        if (!(count.equals(1) || count.equals(2))) throw PortfolioException.get433();
        Collection<String> personIds = getPersonIds(personId);
        List<IndependentDiagnosticDTO> diagnostics = clickHouseRepository.searchIndependentDiagnostic(personIds, start, end, subject);
        for (IndependentDiagnosticDTO dto : diagnostics) {
            IndependentDiagnosticVisible visible =
                    independentDiagnosticVisibleRepository.findFirstByPersonIdAndRecordIdOrderByIdDesc(dto.getPersonId(), dto.getRecordId());
            if (isNull(visible)) {
                visible = new IndependentDiagnosticVisible();
                visible.setRecordId(dto.getRecordId());
                visible.setPersonId(dto.getPersonId());
                visible.setIsVisible(true);
                visible.setChangeDate(LocalDateTime.now());
                independentDiagnosticVisibleRepository.save(visible);
                dto.setIsVisible(true);
            } else {
                dto.setIsVisible(visible.getIsVisible());
            }
        }

        if (count.equals(2)) {
            diagnostics = diagnostics.stream().filter(IndependentDiagnosticDTO::getIsVisible).collect(Collectors.toList());
        }
        diagnostics.sort(Comparator.comparing(IndependentDiagnosticDTO::getEventDate).reversed());

        Long total = (long) diagnostics.size();

        Long totalLevelBelowBase = diagnostics.stream()
                .filter(x -> "Ниже базового".equals(x.getLevelType()))
                .count();

        Long totalLevelBase = diagnostics.stream()
                .filter(x -> "Базовый".equals(x.getLevelType()))
                .count();

        Long totalLevelHight = diagnostics.stream()
                .filter(x -> "Высокий".equals(x.getLevelType()))
                .count();

        Long totalLevelOverHight = diagnostics.stream()
                .filter(x -> "Повышенный".equals(x.getLevelType()))
                .count();

        Double bestResultPercent = null;
        String bestResultSubject = null;
        Short markValue5 = null;
        IndependentDiagnosticDTO maxResult = diagnostics.stream()
                .max(Comparator.comparing(IndependentDiagnosticDTO::getPercentResult)).orElse(null);

        if (nonNull(maxResult)) {
            bestResultPercent = maxResult.getPercentResult();
            bestResultSubject = maxResult.getSubject();
            markValue5 = maxResult.getMarkValue5();
        }

        Map<String, List<Double>> map = new HashMap<>();
        for (IndependentDiagnosticDTO diagnostic : diagnostics) {
            if (isNull(map.get(diagnostic.getSubject()))) {
                List<Double> percent = new ArrayList<>();
                percent.add(diagnostic.getPercentResult());
                map.put(diagnostic.getSubject(), percent);
            } else {
                map.get(diagnostic.getSubject()).add(diagnostic.getPercentResult());
            }
        }

        List<Pair<String, Double>> averageResultPercent = new ArrayList<>();

        for (String key : map.keySet()) {
            double sum = map.get(key).stream().mapToDouble(Double::doubleValue).sum();
            averageResultPercent.add(Pair.of(key, sum / map.get(key).size()));
        }

        return new GetIndependentDiagnosticsResponse(diagnostics,
                total,
                totalLevelBelowBase,
                totalLevelBase,
                totalLevelHight,
                totalLevelOverHight,
                bestResultPercent,
                bestResultSubject,
                markValue5,
                averageResultPercent);
    }

    public List<GetIndependentDiagnosticsByLearningYearResponse> searchIndependentDiagnostic(String personId, String subject,
                                                                                             String share, Integer count) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            PortfolioException.check(Objects.nonNull(shareLink) &&
                    shareLink.getDiagnostic() && shareLink.getStudies(), PortfolioException.get461());
        }

        if (!(count.equals(1) || count.equals(2))) throw PortfolioException.get433();
        Collection<String> personIds = getPersonIds(personId);
        List<IndependentDiagnosticDTO> diagnostics = clickHouseRepository.searchIndependentDiagnostic(personIds, null, null, subject);

        Map<String, List<IndependentDiagnosticDTO>> yearMap = new HashMap<>();

        List<GetIndependentDiagnosticsByLearningYearResponse> result = new ArrayList<>();

        for (IndependentDiagnosticDTO dto : diagnostics) {
            String learningYear;
            if (dto.getEventDate().getMonth().getValue() >= 9) {
                learningYear = dto.getEventDate().getYear() + "-" + (dto.getEventDate().getYear() + 1);
            } else {
                learningYear = (dto.getEventDate().getYear() - 1) + "-" + dto.getEventDate().getYear();
            }

            List<IndependentDiagnosticDTO> yearList = yearMap.get(learningYear);
            if (isNull(yearList)) {
                yearList = new ArrayList<>();
                yearMap.put(learningYear, yearList);
            }
            boolean hasActualDiagnostic = false;

            IndependentDiagnosticDTO currentDto = yearMap.get(learningYear).stream()
                    .filter(x -> x.getWorkId().equals(dto.getWorkId()) && x.getSubject().equals(dto.getSubject()))
                    .findFirst().orElse(null);
            if (nonNull(currentDto) && currentDto.getResultValue() > dto.getResultValue()) {
                hasActualDiagnostic = true;
            } else if (nonNull(currentDto) && currentDto.getResultValue().equals(dto.getResultValue()) &&
                    (currentDto.getEventDate().isAfter(dto.getEventDate()) || currentDto.getEventDate().equals(dto.getEventDate()))) {
                hasActualDiagnostic = true;
            }
            if (!hasActualDiagnostic) {
                IndependentDiagnosticVisible visible =
                        independentDiagnosticVisibleRepository.findFirstByPersonIdAndRecordIdOrderByIdDesc(dto.getPersonId(), dto.getRecordId());
                if (isNull(visible)) {
                    visible = new IndependentDiagnosticVisible();
                    visible.setRecordId(dto.getRecordId());
                    visible.setPersonId(dto.getPersonId());
                    visible.setIsVisible(true);
                    visible.setChangeDate(LocalDateTime.now());
                    independentDiagnosticVisibleRepository.save(visible);
                    dto.setIsVisible(true);
                } else {
                    dto.setIsVisible(visible.getIsVisible());
                }
                yearMap.put(learningYear, yearList);
                yearList.add(dto);
            }
        }

        for (String yearKey : yearMap.keySet()) {
            if (count.equals(2)) {
                List<IndependentDiagnosticDTO> yearValues = yearMap.get(yearKey).stream().filter(IndependentDiagnosticDTO::getIsVisible).collect(Collectors.toList());
                yearMap.put(yearKey, yearValues);
            }
            yearMap.get(yearKey).sort(Comparator.comparing(IndependentDiagnosticDTO::getEventDate).reversed());

            Long total = (long) yearMap.get(yearKey).size();

            Long totalLevelBelowBase = yearMap.get(yearKey).stream()
                    .filter(x -> "Ниже базового".equals(x.getLevelType()))
                    .count();

            Long totalLevelBase = yearMap.get(yearKey).stream()
                    .filter(x -> "Базовый".equals(x.getLevelType()))
                    .count();

            Long totalLevelHight = yearMap.get(yearKey).stream()
                    .filter(x -> "Высокий".equals(x.getLevelType()))
                    .count();

            Long totalLevelOverHight = yearMap.get(yearKey).stream()
                    .filter(x -> "Повышенный".equals(x.getLevelType()))
                    .count();

            Double bestResultPercent = null;
            String bestResultSubject = null;
            Integer bestResultValue = null;
            Integer bestMaxResult = null;
            LocalDate bestEventDate = null;
            String bestLevelType = null;
            Short markValue5 = null;

            IndependentDiagnosticDTO maxResult = yearMap.get(yearKey).stream()
                    .max(Comparator.comparing(IndependentDiagnosticDTO::getPercentResult)).orElse(null);

            if (nonNull(maxResult)) {
                bestResultPercent = maxResult.getPercentResult();
                bestResultSubject = maxResult.getSubject();
                bestResultValue = maxResult.getResultValue();
                bestMaxResult = maxResult.getMaxResult();
                bestEventDate = maxResult.getEventDate();
                bestLevelType = maxResult.getLevelType();
                markValue5 = maxResult.getMarkValue5();
            }

            Map<String, List<Double>> map = new HashMap<>();
            for (IndependentDiagnosticDTO diagnostic : yearMap.get(yearKey)) {
                if (isNull(map.get(diagnostic.getSubject()))) {
                    List<Double> percent = new ArrayList<>();
                    percent.add(diagnostic.getPercentResult());
                    map.put(diagnostic.getSubject(), percent);
                } else {
                    map.get(diagnostic.getSubject()).add(diagnostic.getPercentResult());
                }
            }

            List<Pair<String, Double>> averageResultPercent = new ArrayList<>();

            for (String key : map.keySet()) {
                double sum = map.get(key).stream().mapToDouble(Double::doubleValue).sum();
                averageResultPercent.add(Pair.of(key, sum / map.get(key).size()));
            }

            GetIndependentDiagnosticsByLearningYearResponse responseObject = new GetIndependentDiagnosticsByLearningYearResponse();
            responseObject.fillDiagnostic(yearKey,
                    yearMap.get(yearKey),
                    total,
                    totalLevelBelowBase,
                    totalLevelBase,
                    totalLevelHight,
                    totalLevelOverHight,
                    bestResultPercent,
                    bestResultSubject,
                    bestResultValue,
                    bestMaxResult,
                    bestEventDate,
                    bestLevelType,
                    markValue5,
                    averageResultPercent);
            result.add(responseObject);
        }
        return result;
    }

    public List<GetPersonalDiagnosticResponse> searchPersonalDiagnostic(String personId, String subject, String share, Integer count) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = parseCookie(share);
            PortfolioException.check(Objects.nonNull(shareLink) &&
                    shareLink.getDiagnostic() && shareLink.getStudies() && shareLink.getPersonalDiagnostic(), PortfolioException.get461());
        }
        if (!(count.equals(1) || count.equals(2))) throw PortfolioException.get433();
        Collection<String> personIds = getPersonIds(personId);
        List<PersonalDiagnosticDTO> diagnostics = clickHouseRepository.searchPersonalDiagnostic(personIds, subject);
        for (PersonalDiagnosticDTO dto : diagnostics) {
            IndependentDiagnosticVisible visible =
                    independentDiagnosticVisibleRepository.findFirstByPersonIdAndRecordIdOrderByIdDesc(dto.getPersonId(), dto.getRecordId());
            if (isNull(visible)) {
                visible = new IndependentDiagnosticVisible();
                visible.setRecordId(dto.getRecordId());
                visible.setPersonId(dto.getPersonId());
                visible.setIsVisible(true);
                visible.setChangeDate(LocalDateTime.now());
                independentDiagnosticVisibleRepository.save(visible);
                dto.setIsVisible(true);
            } else {
                dto.setIsVisible(visible.getIsVisible());
            }
        }

        Map<String, List<PersonalDiagnosticDTO>> yearMap = new HashMap<>();

        List<GetPersonalDiagnosticResponse> result = new ArrayList<>();

        for (PersonalDiagnosticDTO dto : diagnostics) {
            String learningYear;
            if (dto.getEventDate().getMonth().getValue() >= 9) {
                learningYear = dto.getEventDate().getYear() + "-" + (dto.getEventDate().getYear() + 1);
            } else {
                learningYear = (dto.getEventDate().getYear() - 1) + "-" + dto.getEventDate().getYear();
            }

            List<PersonalDiagnosticDTO> yearList = yearMap.get(learningYear);
            if (isNull(yearList)) {
                yearList = new ArrayList<>();
                yearMap.put(learningYear, yearList);
            }
            boolean hasActualDiagnostic = false;

            PersonalDiagnosticDTO currentDto = yearMap.get(learningYear).stream()
                    .filter(x -> x.getWorkId().equals(dto.getWorkId()) && x.getSubject().equals(dto.getSubject()))
                    .findFirst().orElse(null);
            if (nonNull(currentDto) && currentDto.getResultValue() > dto.getResultValue()) {
                hasActualDiagnostic = true;
            } else if (nonNull(currentDto) && currentDto.getResultValue().equals(dto.getResultValue()) &&
                    (currentDto.getEventDate().isAfter(dto.getEventDate()) || currentDto.getEventDate().equals(dto.getEventDate()))) {
                hasActualDiagnostic = true;
            }
            if (!hasActualDiagnostic) {
                IndependentDiagnosticVisible visible =
                        independentDiagnosticVisibleRepository.findFirstByPersonIdAndRecordIdOrderByIdDesc(dto.getPersonId(), dto.getRecordId());
                if (isNull(visible)) {
                    visible = new IndependentDiagnosticVisible();
                    visible.setRecordId(dto.getRecordId());
                    visible.setPersonId(dto.getPersonId());
                    visible.setIsVisible(true);
                    visible.setChangeDate(LocalDateTime.now());
                    independentDiagnosticVisibleRepository.save(visible);
                    dto.setIsVisible(true);
                } else {
                    dto.setIsVisible(visible.getIsVisible());
                }
                yearMap.put(learningYear, yearList);
                yearList.add(dto);
            }
        }

        for (String yearKey : yearMap.keySet()) {
            List<PersonalDiagnosticDTO> yearValues = yearMap.get(yearKey);
            if (count.equals(2)) {
                yearValues = yearValues.stream().filter(PersonalDiagnosticDTO::getIsVisible).collect(Collectors.toList());
            }
            yearValues.sort(Comparator.comparing(PersonalDiagnosticDTO::getEventDate).reversed());
            GetPersonalDiagnosticResponse year = new GetPersonalDiagnosticResponse();
            year.setLearningYear(yearKey);

            GetPersonalDiagnosticResponse.BestResult bestResult = new GetPersonalDiagnosticResponse.BestResult();
            PersonalDiagnosticDTO bestWorkInYear = yearValues.stream().max(Comparator.comparingDouble(PersonalDiagnosticDTO::getPercentResult)).orElse(null);
            if (Objects.nonNull(bestWorkInYear)) {
                bestResult.setBestResultValue(bestWorkInYear.getResultValue());
                bestResult.setBestResultPercent(bestWorkInYear.getPercentResult());
                bestResult.setBestResultSubject(bestWorkInYear.getSubject());
                bestResult.setBestResultEventDate(bestWorkInYear.getEventDate());
            }
            year.setBestResult(bestResult);
            year.setDiagnostics(yearValues);
            result.add(year);
        }

        return result;
    }

    //А_ВН_12
    private List<EventDTO> getOlympiadsFromClickhouse(String actualPersonId, Collection<String> personIds) {
        List<OlympiadDTO.OlympiadClickDTO> olympiadClickDTOS = clickHouseRepository.getOlympiads(personIds);
        return mapOlympiadsFromClickHouse(olympiadClickDTOS, actualPersonId);
    }

    private void fillCodeLists(ShareLink shareLink, List<Integer> typeCodes, List<Integer> achievementCodes, Integer code) {
        if (shareLink.getSport()) {
            achievementCodes.add(1);
            if (code.equals(EVENT.getCode())) {
                checkShareLinkParam(shareLink.getSportGames(), typeCodes, 28); //event
                checkShareLinkParam(shareLink.getHike(), typeCodes, 29, 30);
            }
            if (code.equals(EMPLOYMENT.getCode()))
                checkShareLinkParam(shareLink.getSportUnit(), typeCodes, 24, 25, 26);//employment
            if (code.equals(REWARD.getCode()) || code.equals(SPORT_REWARD.getCode()))
                checkShareLinkParam(shareLink.getSportReward(), typeCodes, 32, 33, 34, 35, 36); //
            if (code.equals(AFFILATION.getCode()))
                checkShareLinkParam(shareLink.getSportClub(), typeCodes, 21, 22, 75); //affilation
        }
        if (shareLink.getCreation()) {
            achievementCodes.add(2);
            if (code.equals(EVENT.getCode()))
                checkShareLinkParam(shareLink.getCreationContest(), typeCodes, 44); //event
            if (code.equals(EMPLOYMENT.getCode()))
                checkShareLinkParam(shareLink.getCreationUnit(), typeCodes, 41, 42); //employment
            if (code.equals(REWARD.getCode()))
                checkShareLinkParam(shareLink.getCreationReward(), typeCodes, 46); //reward
            if (code.equals(AFFILATION.getCode()))
                checkShareLinkParam(shareLink.getCreationClub(), typeCodes, 38, 39); //affilation
        }
        if (shareLink.getCivil()) {
            achievementCodes.add(6);
            achievementCodes.add(7);
            if (code.equals(EVENT.getCode()))
                checkShareLinkParam(shareLink.getCivilContest(), typeCodes, 55, 56, 57, 58, 59); //event
            if (code.equals(EMPLOYMENT.getCode()))
                checkShareLinkParam(shareLink.getCivilUnit(), typeCodes, 53); //employment
            if (code.equals(REWARD.getCode()))
                checkShareLinkParam(shareLink.getCivilReward(), typeCodes, 61, 62); //reward
            if (code.equals(AFFILATION.getCode()))
                checkShareLinkParam(shareLink.getCivilClub(), typeCodes, 50, 51); //affilation
        }
        if (shareLink.getCulture()) {
            if (code.equals(EVENT.getCode())) {
                checkShareLinkParam(shareLink.getOfflineVisit(), typeCodes, 48); //event
                checkShareLinkParam(shareLink.getOnlineVisit(), typeCodes, 63);
            }
        }
        if (shareLink.getStudies() && shareLink.getOlympiads()) { //event and reward
            typeCodes.add(8);
            typeCodes.add(10);
        }
        if (shareLink.getScience()) {
            achievementCodes.add(3);
            achievementCodes.add(4);
            achievementCodes.add(5);
            if (code.equals(EVENT.getCode()))
                checkShareLinkParam(shareLink.getScienceContest(), typeCodes, 15, 16); //event
            if (code.equals(EMPLOYMENT.getCode()))
                checkShareLinkParam(shareLink.getScienceEmployments(), typeCodes, 13); //employment
            if (code.equals(REWARD.getCode()))
                checkShareLinkParam(shareLink.getScienceReward(), typeCodes, 18, 19); //reward
            if (code.equals(PersonallyEntityEnum.PROJECT.getCode()))
                checkShareLinkParam(shareLink.getProjects(), typeCodes, 11);
        }
        if (shareLink.getProfile()) { //profile
            typeCodes.add(66);
            typeCodes.add(67);
        }
        if (shareLink.getProfession()) { //profession
            if (code.equals(EVENT.getCode()))
                checkShareLinkParam(shareLink.getProfessionEvents(), typeCodes, 70, 72); //event
            if (code.equals(GIA_WORLDSKILLS.getCode()))
                checkShareLinkParam(shareLink.getProfessionWorldskills(), typeCodes, 71, 73); //gia_worldskills
            if (code.equals(REWARD.getCode()))
                checkShareLinkParam(shareLink.getProfessionRewards(), typeCodes, 69, 74); //reward
        }
    }

    private void checkShareLinkParam(Boolean link, List<Integer> typeCodes, Integer... code) {
        if (link) typeCodes.addAll(Arrays.asList(code));
    }

    @SneakyThrows
    private void validateShareLink(ShareLink shareLink, Integer code) {
        List<String> fieldNames = new ArrayList<>();
        switch (code) {
            case (2): //event
                fieldNames.add("sportGames");
                fieldNames.add("hike");
                fieldNames.add("creationContest");
                fieldNames.add("civilContest");
                fieldNames.add("offlineVisit");
                fieldNames.add("onlineVisit");
                fieldNames.add("olympiads");
                fieldNames.add("scienceContest");
                break;
            case (1): //employments
                fieldNames.add("sportUnit");
                fieldNames.add("creationUnit");
                fieldNames.add("civilUnit");
                fieldNames.add("scienceEmployments");
                break;
            case (3): //reward
                fieldNames.add("sportReward");
                fieldNames.add("creationReward");
                fieldNames.add("civilReward");
                break;
            case (4): //sport_reward
                fieldNames.add("sportReward");
                break;
            case (5): //affilation
                fieldNames.add("sportClub");
                fieldNames.add("creationClub");
                fieldNames.add("civilClub");
                break;
            case (6): //project
                fieldNames.add("projects");
                break;
        }
        fieldNames.add("isActive");
        fieldNames.add("studies");
        fieldNames.add("science");
        fieldNames.add("sport");
        fieldNames.add("creation");
        fieldNames.add("culture");
        fieldNames.add("civil");
        Boolean allTrue = true;
        Boolean hasWasteFields = false;
        Field[] fields = shareLink.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.get(shareLink).equals(Boolean.FALSE)) allTrue = false;
            if (field.getType().equals(Boolean.class) && !fieldNames.contains(field.getName())) {
                if (field.get(shareLink).equals(Boolean.TRUE)) hasWasteFields = true;
            }
        }
        PortfolioException.check(allTrue || !hasWasteFields, PortfolioException.get461());
    }


    public List<EmploymentDTO> mapToEmployment(List<AchievementClickDTO> achievements, String personId) {
        List<EmploymentDTO> employments = new ArrayList<>();
        List<SectionRef> sectionRefs = crudService.findAllRefs(SectionRef.class);
        for (AchievementClickDTO achievement : achievements) {
            EmploymentDTO employment = new EmploymentDTO();
            employment.setRecordId(achievement.getRecordId().toString());
            employment.setPersonId(personId);
            employment.setName(achievement.getName());
            employment.setDescription(achievement.getDescription());
            employment.setStartDate(achievement.getEventDate());
            employment.setCreationDate(achievement.getEventTime());
            employment.setEndDate(achievement.getActivityDate());

            SectionEnum sectionEnum = SectionEnum.getClickHouseMap().get(achievement.getCategory());
            SectionRef sectionRef = new SectionRef();
            sectionRef.setCode(sectionEnum.getCode());
            sectionRef.setValue(sectionEnum.getValue());
            employment.setCategory(sectionRef);

            DataSourceRef source = new DataSourceRef();
            source.setValue(achievement.getSource());
            source.setCode(DataSourceEnum.getMap().get(achievement.getSource()));
            employment.setSource(source);

            AchievementActivityFormatRef format = new AchievementActivityFormatRef();
            format.setValue(achievement.getFormat());
            if (achievement.getFormat().equals("Очное")) {
                format.setCode(1);
            } else {
                format.setCode(2);
            }
            employment.setAchievementActivityFormat(format);

            AchievementCategoryEnum category = AchievementCategoryEnum.getByCode(achievement.getCategory());
            if (Objects.nonNull(category)) {
                employment.setCategory(sectionRefs.stream().filter(x -> x.getCode().equals(category.getCategoryCode()))
                        .findFirst().orElse(null));
                employment.setDataKind(category.getDataKind());
                employment.setType(sectionRefs.stream().filter(x -> x.getCode().equals(category.getTypeCode()))
                        .findFirst().orElse(null));

                employments.add(employment);
            }
        }
        return employments;
    }

    public List<EventDTO> mapToEvents(List<AchievementClickDTO> achievements, String personId) {
        List<EventDTO> events = new ArrayList<>();
        List<SectionRef> sectionRefs = crudService.findAllRefs(SectionRef.class);
        for (AchievementClickDTO achievement : achievements) {
            EventDTO event = new EventDTO();
            event.setRecordId(achievement.getRecordId().toString());
            event.setPersonId(personId);
            event.setName(achievement.getName());
            event.setDescription(achievement.getDescription());
            event.setStartDate(achievement.getEventDate());
            event.setCreationDate(achievement.getEventTime());
            event.setEndDate(achievement.getActivityDate());

            SectionEnum sectionEnum = SectionEnum.getClickHouseMap().get(achievement.getCategory());
            SectionRef sectionRef = new SectionRef();
            sectionRef.setCode(sectionEnum.getCode());
            sectionRef.setValue(sectionEnum.getValue());
            event.setCategory(sectionRef);
            event.setCategoryCode(sectionRef.getCode());

            DataSourceRef source = new DataSourceRef();
            source.setValue(achievement.getSource());
            source.setCode(DataSourceEnum.getMap().get(achievement.getSource()));
            event.setSource(source);

            AchievementCategoryEnum category = AchievementCategoryEnum.getByCode(achievement.getCategory());
            if (Objects.nonNull(category)) {
                event.setCategory(sectionRefs.stream().filter(x -> x.getCode().equals(category.getCategoryCode()))
                        .findFirst().orElse(null));
                event.setDataKind(category.getDataKind());
                event.setTypeCode(category.getTypeCode().longValue());

                events.add(event);
            }
        }
        return events;
    }

    public List<RewardDTO> mapToReward(List<AchievementClickDTO> achievements, String personId) {
        List<RewardDTO> rewards = new ArrayList<>();

        List<SectionRef> sectionRefs = crudService.findAllRefs(SectionRef.class);
        for (AchievementClickDTO achievement : achievements) {
            RewardDTO reward = new RewardDTO();
            reward.setRecordId(achievement.getRecordId().toString());
            reward.setPersonId(personId);
            reward.setName(achievement.getName());
            reward.setDescription(achievement.getDescription());
            reward.setDate(achievement.getEventDate());
            reward.setCreationDate(achievement.getEventTime());

            SectionEnum sectionEnum = SectionEnum.getClickHouseMap().get(achievement.getCategory());
            SectionRef sectionRef = new SectionRef();
            sectionRef.setCode(sectionEnum.getCode());
            sectionRef.setValue(sectionEnum.getValue());
            reward.setCategory(sectionRef);

            DataSourceRef source = new DataSourceRef();
            source.setValue(achievement.getSource());
            source.setCode(DataSourceEnum.getMap().get(achievement.getSource()));
            reward.setSource(source);

            AchievementCategoryEnum category = AchievementCategoryEnum.getByCode(achievement.getCategory());
            if (Objects.nonNull(category)) {
                reward.setCategory(sectionRefs.stream().filter(x -> x.getCode().equals(category.getCategoryCode()))
                        .findFirst().orElse(null));
                reward.setDataKind(category.getDataKind());
                reward.setTypeCode(category.getTypeCode().longValue());

                rewards.add(reward);
            }
        }
        return rewards;
    }

    public List<SportRewardDTO> mapToSportReward(List<AchievementClickDTO> achievements, String personId) { //todo remove duplicates
        List<SportRewardDTO> rewards = new ArrayList<>();

        List<SectionRef> sectionRefs = crudService.findAllRefs(SectionRef.class);
        for (AchievementClickDTO achievement : achievements) {
            SportRewardDTO reward = new SportRewardDTO();
            reward.setRecordId(achievement.getRecordId().toString());
            reward.setPersonId(personId);
            reward.setName(achievement.getName());
            reward.setDescription(achievement.getDescription());
            reward.setDate(achievement.getEventDate());
            reward.setCreationDate(achievement.getEventTime());

            SectionEnum sectionEnum = SectionEnum.getClickHouseMap().get(achievement.getCategory());
            SectionRef sectionRef = new SectionRef();
            sectionRef.setCode(sectionEnum.getCode());
            sectionRef.setValue(sectionEnum.getValue());
            reward.setCategory(sectionRef);

            DataSourceRef source = new DataSourceRef();
            source.setValue(achievement.getSource());
            source.setCode(DataSourceEnum.getMap().get(achievement.getSource()));
            reward.setSource(source);

            AchievementCategoryEnum category = AchievementCategoryEnum.getByCode(achievement.getCategory());
            if (Objects.nonNull(category)) {
                reward.setCategory(sectionRefs.stream().filter(x -> x.getCode().equals(category.getCategoryCode()))
                        .findFirst().orElse(null));
                reward.setDataKind(category.getDataKind());
                reward.setType(sectionRefs.stream().filter(x -> x.getCode().equals(category.getTypeCode()))
                        .findFirst().orElse(null));

                rewards.add(reward);
            }
        }
        return rewards;
    }

    public List<ProjectDTO> mapToProject(List<AchievementClickDTO> achievements, String personId) {
        List<ProjectDTO> projects = new ArrayList<>();

        List<SectionRef> sectionRefs = crudService.findAllRefs(SectionRef.class);
        for (AchievementClickDTO achievement : achievements) {
            ProjectDTO project = new ProjectDTO();
            project.setRecordId(achievement.getRecordId().toString());
            project.setPersonId(personId);
            project.setName(achievement.getName());
            project.setDescription(achievement.getDescription());
            project.setStartDate(achievement.getEventDate());
            project.setCreationDate(achievement.getEventTime());
            project.setEndDate(achievement.getActivityDate());

            SectionEnum sectionEnum = SectionEnum.getClickHouseMap().get(achievement.getCategory());
            SectionRef sectionRef = new SectionRef();
            sectionRef.setCode(sectionEnum.getCode());
            sectionRef.setValue(sectionEnum.getValue());
            project.setCategory(sectionRef);

            DataSourceRef source = new DataSourceRef();
            source.setValue(achievement.getSource());
            source.setCode(DataSourceEnum.getMap().get(achievement.getSource()));
            project.setSource(source);

            AchievementCategoryEnum category = AchievementCategoryEnum.getByCode(achievement.getCategory());
            if (Objects.nonNull(category)) {
                project.setCategory(sectionRefs.stream().filter(x -> x.getCode().equals(category.getCategoryCode()))
                        .findFirst().orElse(null));
                project.setDataKind(category.getDataKind());

                projects.add(project);
            }

        }
        return projects;
    }

    public List<ProfessionalEducationJobResponse.Job> mapToJob(List<ProftechJobDTO> proftechJobDTOs, String personId) {
        List<ProfessionalEducationJobResponse.Job> jobs = new ArrayList<>();
        for (ProftechJobDTO proftechJobDTO : proftechJobDTOs) {
            ProfessionalEducationJobResponse.Job job = new ProfessionalEducationJobResponse.Job();
            job.setRecordId(proftechJobDTO.getRecordId());

            job.setPosition(proftechJobDTO.getPosition());
            job.setMainFunctionality(proftechJobDTO.getMainFunctionality());
            job.setIsByProfile(proftechJobDTO.getIsByProfile());
            job.setOrganization(proftechJobDTO.getOrganization());
            job.setCreationDate(proftechJobDTO.getCreationDate());

            ProfessionalEducationJobResponse.CodeValue employmentDocTypeRef = new ProfessionalEducationJobResponse.CodeValue();
            employmentDocTypeRef.setCode(proftechJobDTO.getEmploymentTypeId());
            employmentDocTypeRef.setValue(proftechJobDTO.getEmploymentType());
            job.setEmploymentType(employmentDocTypeRef);

            ProfessionalEducationJobResponse.CodeValue levelBusinessRef = new ProfessionalEducationJobResponse.CodeValue();
            levelBusinessRef.setValue(proftechJobDTO.getLevelBusiness());
            levelBusinessRef.setCode(proftechJobDTO.getLevelBusinessId());
            job.setBusinessLevel(levelBusinessRef);

            ProfessionalEducationJobResponse.CodeValue salaryRangeRef = new ProfessionalEducationJobResponse.CodeValue();
            salaryRangeRef.setValue(proftechJobDTO.getSalaryRange());
            salaryRangeRef.setCode(proftechJobDTO.getSalaryRangeId());
            job.setSalaryRange(salaryRangeRef);

            ProfessionalEducationJobResponse.Contract contract = new ProfessionalEducationJobResponse.Contract();
            contract.setIsContract(proftechJobDTO.getIsDocForEmployment());
            contract.setDate(proftechJobDTO.getDocEmploymentDate());
            contract.setType(buildCodeValue(proftechJobDTO.getDocTypeId(), proftechJobDTO.getDocType()));
            job.setContract(contract);

            job.setSource(crudService.findFirstRef(DataSourceRef.class, 16));

        }
        return jobs;
    }

    private ProfessionalEducationJobResponse.CodeValue buildCodeValue(Integer code, String value) {
        ProfessionalEducationJobResponse.CodeValue codeValue = new ProfessionalEducationJobResponse.CodeValue();
        codeValue.setCode(code);
        codeValue.setValue(value);
        return codeValue;
    }

    private List<EventDTO> mapOlympiadsFromClickHouse(List<OlympiadDTO.OlympiadClickDTO> olympiads, String
            personId) {
        List<EventDTO> events = new ArrayList<>();
        for (OlympiadDTO.OlympiadClickDTO olympiad : olympiads) {
            EventDTO event = new EventDTO();
            event.setRecordId(olympiad.getRecordId().toString());
            event.setPersonId(personId);
            event.setOrganizators(olympiad.getOrganizators());
            event.setName(olympiad.getName());
            event.setCreationDate(olympiad.getEventTime());
            event.setProfile(olympiad.getProfile());
            event.setParallelsCode(olympiad.getParallels());
            event.setResult(olympiad.getStatus());
            event.setStartDate(olympiad.getDate());
            // event.setSubjectCode(olympiad.getSubjects());
            event.getMap().put("subjects", olympiad.ref());
            event.setCategoryCode(1);

            DataSourceRef dataSourceRef = new DataSourceRef(); //fixme refactor
            dataSourceRef.setCode(3);
            dataSourceRef.setValue("Олимпиады");
            event.setSource(dataSourceRef);

            OlympiadTypeRef levelEvent = new OlympiadTypeRef();
            levelEvent.setCode(OlympiadTypeEnum.getMap().get(olympiad.getType()));
            levelEvent.setValue(olympiad.getType());
            event.setLevelEvent(levelEvent);

            if (StringUtils.isNotBlank(olympiad.getFormat())) {
                OlympiadFormatRef format = new OlympiadFormatRef();
                format.setValue(olympiad.getFormat());
                if (olympiad.getFormat().equals("Очное")) {
                    format.setCode(1);
                } else {
                    format.setCode(2);
                }
                event.setFormat(format);
            }

            events.add(event);
        }
        return events;
    }


    public List<EmploymentDTO> mapToEmploymentDto(List<Employment> employments) {
        List<EmploymentDTO> result = new ArrayList<>();
        for (Employment employment : employments) {
            EmploymentDTO dto = new EmploymentDTO();
            BeanUtils.copyProperties(employment, dto);
            dto.setSource(employment.getSource());
            dto.setCategory(employment.getCategory());
            dto.setType(employment.getType());
            dto.setSubcategory(employment.getSubcategory());
            dto.setAchievementActivityFormat(employment.getAchievementActivityFormat());
            dto.setSportKind(employment.getSportKind());
            dto.setTourismKind(employment.getTourismKind());
            result.add(dto);
        }
        return result;
    }

    public List<EventDTO> mapToEventDto(List<Event> events) {
        List<EventDTO> result = new ArrayList<>();
        for (Event event : events) {
            EventDTO dto = new EventDTO();
            BeanUtils.copyProperties(event, dto);
            dto.setSource(event.getSource());
            dto.setCategory(event.getCategory());
            dto.setLevelEvent(event.getLevelEvent());
            dto.setSubcategory(event.getSubcategory());
            dto.setFormat(event.getFormat());
            dto.setSportKind(event.getSportKind());
            dto.setTourismKind(event.getTourismKind());
            result.add(dto);
        }
        return result;
    }

    public List<EventDTO> mapToEventDtoWithOlympiadResult(List<Event> events,
                                                          Map<Long, List<OlympiadOutcome>> olympiadOutcomeMap) {
        List<EventDTO> result = new ArrayList<>();
        for (Event event : events) {
            EventDTO dto = new EventDTO();
            BeanUtils.copyProperties(event, dto);
            dto.setSource(event.getSource());
            dto.setCategory(event.getCategory());
            dto.setLevelEvent(event.getLevelEvent());
            dto.setSubcategory(event.getSubcategory());
            dto.setFormat(event.getFormat());
            dto.setSportKind(event.getSportKind());
            dto.setTourismKind(event.getTourismKind());
            dto.setOlympiadResult(mapToOlympiadResultDto(olympiadOutcomeMap.get(event.getId())));
            result.add(dto);
        }
        return result;
    }

    private List<OlympiadResultDto> mapToOlympiadResultDto(List<OlympiadOutcome> olympiadOutcomes) {
        List<OlympiadResultDto> olympiadResult = new ArrayList<>();

        if (CollectionUtils.isEmpty(olympiadOutcomes)) {
            return olympiadResult;
        }

        for (OlympiadOutcome olympiadOutcome : olympiadOutcomes) {
            OlympiadResultDto olympiadResultDto = new OlympiadResultDto();

            olympiadResultDto.setEventId(olympiadOutcome.getEventId().getId());
            olympiadResultDto.setContestScoreId(olympiadOutcome.getContestScoreId());
            olympiadResultDto.setOlympiadTypeCode(olympiadOutcome.getOlympiadPartCode().getCode());
            olympiadResultDto.setNameContest(olympiadOutcome.getNameContest());
            olympiadResultDto.setContestScore(olympiadOutcome.getContestScore());
            olympiadResultDto.setMaxContestScore(olympiadOutcome.getMaxContestScore());

            olympiadResult.add(olympiadResultDto);
        }

        return olympiadResult;
    }

    public List<ProjectDTO> mapToProjectDto(List<Project> projects) {
        List<ProjectDTO> result = new ArrayList<>();
        for (Project project : projects) {
            ProjectDTO dto = new ProjectDTO();
            BeanUtils.copyProperties(project, dto);
            dto.setSource(project.getSource());
            dto.setCategory(project.getCategory());
            dto.setLevelProject(project.getLevelProject());
            dto.setProjectFormat(project.getProjectFormat());
            dto.setSubcategory(project.getSubcategory());
            result.add(dto);
        }
        return result;
    }

    public List<RewardDTO> mapToRewardDto(List<Reward> rewards) {
        List<RewardDTO> result = new ArrayList<>();
        for (Reward reward : rewards) {
            RewardDTO dto = new RewardDTO();
            BeanUtils.copyProperties(reward, dto);
            dto.setSource(reward.getSource());
            dto.setCategory(reward.getCategory());
            dto.setStatusReward(reward.getStatusReward());
            dto.setLevelReward(reward.getLevelReward());
            dto.setRewardType(reward.getRewardType());
            dto.setSubcategory(reward.getSubcategory());
            result.add(dto);
        }
        return result;
    }

    public List<SportRewardDTO> mapToSportRewardDto(List<SportReward> rewards) {
        List<SportRewardDTO> result = new ArrayList<>();
        for (SportReward reward : rewards) {
            SportRewardDTO dto = new SportRewardDTO();
            BeanUtils.copyProperties(reward, dto);
            dto.setSource(reward.getSource());
            dto.setCategory(reward.getCategory());
            dto.setLevelReward(reward.getLevelReward());
            dto.setSubcategory(reward.getSubcategory());
            dto.setAgeLimit(reward.getAgeLimit());
            dto.setLevelReward(reward.getLevelReward());
            dto.setSportKind(reward.getSportKind());
            result.add(dto);
        }
        return result;
    }

    public List<GeneralRatingResponse> getGeneralRating(String personId) {
        List<GeneralDiagnosticRatingDTO> generalRegionRatingDTO =
                clickHouseRepository.searchGeneralDiagnosticRegion(personId);
        if (generalRegionRatingDTO.isEmpty()) {
            return new ArrayList<>();
        }

        PersonDTO person = contingentService.getPerson(personId);
        EducationDTO educationDTO = safetyGet(() -> person.getEducation().stream()
                .filter(y -> y.getServiceTypeId().equals(2))
                .max(comparing(EducationDTO::getBegin))
//                .filter(y -> nonNull(y.getParallelId()))
//                .max(comparing(EducationDTO::getParallelId))
                .orElse(null));

        String schoolId;
        if (Objects.nonNull(educationDTO) && Objects.nonNull(educationDTO.getOrganizationId())) {
            schoolId = educationDTO.getOrganizationId().toString();
        } else {
            schoolId = clickHouseRepository.searchLatestDiagnostic(personId);
        }
        PortfolioException.check(Objects.nonNull(schoolId), PortfolioException.get487("SchoolId"));

        List<PersonDTO> personsFromClass = contingentService.getPersonsFromClass(
                schoolId, educationDTO.getClassDTO().getName(), educationDTO.getClassDTO().getUid(), educationDTO.getEnd());
        List<String> personIds = personsFromClass.stream().map(PersonDTO::getPersonId)
                .map(Object::toString).collect(Collectors.toList());

        List<GeneralDiagnosticRatingDTO> generalSchoolRatingDTORaw =
                clickHouseRepository.searchGeneralDiagnosticSchool(personId);

        List<GeneralDiagnosticRatingDTO> generalSchoolRatingDTO = new ArrayList<>();
        generalSchoolRatingDTORaw.forEach(x -> {
            GeneralDiagnosticRatingDTO generalDiagnosticRatingDTO = generalSchoolRatingDTORaw.stream()
                    .filter(z -> z.getLearningYear().equals(x.getLearningYear()))
                    .filter(z -> z.getDiagnosticSubjectName().equals(x.getDiagnosticSubjectName()))
                    .max(comparing(GeneralDiagnosticRatingDTO::getAveragePercentExecutionResult)).orElse(null);
            if (!generalSchoolRatingDTO.contains(generalDiagnosticRatingDTO) && Objects.nonNull(generalDiagnosticRatingDTO)) {
                GeneralDiagnosticRatingDTO regionalRatingDto = generalRegionRatingDTO.stream()
                        .filter(z -> z.getLearningYear().equals(generalDiagnosticRatingDTO.getLearningYear()))
                        .filter(z -> z.getDiagnosticSubjectName().equals(generalDiagnosticRatingDTO.getDiagnosticSubjectName()))
                        .findFirst().orElse(null);
                if (Objects.nonNull(regionalRatingDto)) {
                    generalDiagnosticRatingDTO.setAveragePercentExecutionResult(round(regionalRatingDto.getAveragePercentExecutionResult(), 1));
                }
                generalSchoolRatingDTO.add(generalDiagnosticRatingDTO);
            }
        });


        List<GeneralRatingDistinctInfo> generalDistinctPercentRegion =
                clickHouseRepository.searchDistinctAverageGeneralResultRegion();
        List<GeneralRatingDistinctInfo> generalDistinctPercentSchool =
                clickHouseRepository.searchDistinctAverageGeneralResultSchool(schoolId);
        List<GeneralRatingDistinctInfo> generalDistinctPercentClass =
                clickHouseRepository.searchDistinctAverageGeneralResultClass(personIds);

        List<String> regionSubjects = generalRegionRatingDTO.stream()
                .map(GeneralDiagnosticRatingDTO::getDiagnosticSubjectName).distinct()
                .collect(Collectors.toList());

        List<String> schoolSubjects = generalSchoolRatingDTO.stream()
                .map(GeneralDiagnosticRatingDTO::getDiagnosticSubjectName).distinct()
                .collect(Collectors.toList());

        List<CountPercentsBySubjects> allPercentsRegion =
                clickHouseRepository.searchCountPercentsRegion(regionSubjects);
        List<CountPercentsBySubjects> allPercentsSchool =
                clickHouseRepository.searchCountPercentsSchool(schoolSubjects, schoolId);
        List<CountPercentsBySubjects> allPercentsClass =
                clickHouseRepository.searchCountPercentsClass(schoolSubjects, personIds);

        Set<String> learningYearsGeneral = generalRegionRatingDTO.stream()
                .map(GeneralDiagnosticRatingDTO::getLearningYear)
                .collect(Collectors.toSet());

        List<GeneralRatingResponse> generalRatingResponse = new ArrayList<>();

        for (String learningYear : learningYearsGeneral) {
            GeneralRatingResponse responseRating = new GeneralRatingResponse();
            responseRating.setLearningYear(learningYear);

            List<GeneralRatingResponse.GeneralRating> generalRatings = new ArrayList<>();


            generalRegionRatingDTO.stream().filter(x -> x.getLearningYear().equals(learningYear))
                    .collect(Collectors.toMap(GeneralDiagnosticRatingDTO::getDiagnosticSubjectName, x -> x))
                    .forEach((name, rating) -> {
                        GeneralRatingResponse.GeneralRating generalRating = new GeneralRatingResponse.GeneralRating();
                        generalRating.setSubjectGeneralRatingName(name);

                        GeneralRatingResponse.GeneralRatingEntity ratingRegion = new GeneralRatingResponse.GeneralRatingEntity();
                        ratingRegion.setAverageResultPercentStudent(round(rating.getAveragePercentExecutionResult(), 1));

                        CountPercentsBySubjects lowerPercentsRegion =
                                clickHouseRepository.searchCountLowerPercentsRegion(name, rating.getAveragePercentExecutionResult())
                                        .stream().filter(x -> x.getLearningYear().equals(learningYear))
                                        .findFirst().orElse(null);

                        CountPercentsBySubjects countPercentsBySubject = allPercentsRegion.stream().filter(x -> x.getLearningYear().equals(learningYear))
                                .filter(x -> x.getDiagnosticSubjectName().equals(name)).findFirst().orElse(null);

                        if (Objects.nonNull(countPercentsBySubject) && Objects.nonNull(lowerPercentsRegion)) {
                            ratingRegion.setPercentLowerOthers(round(100 / countPercentsBySubject.getCountPercents() * lowerPercentsRegion.getCountPercents(), 1));
                        } else {
                            ratingRegion.setPercentLowerOthers(0.0);
                        }

                        GeneralRatingDistinctInfo generalRatingDistinctRegionInfo = generalDistinctPercentRegion.stream()
                                .filter(a -> a.getLearningYear().equals(learningYear))
                                .filter(a -> a.getDiagnosticSubjectName().equals(name))
                                .findFirst().orElse(null);
                        if (Objects.nonNull(generalRatingDistinctRegionInfo)) {
                            ratingRegion.setPlaceGeneralRatingStudent(generalRatingDistinctRegionInfo.getDistinctPercentResult()
                                    .indexOf(rating.getAveragePercentExecutionResult()) + 1);
                            ratingRegion.setAverageResultPercentFirstPlace(generalRatingDistinctRegionInfo.getDistinctPercentResult().get(0));
                            if (generalRatingDistinctRegionInfo.getDistinctPercentResult().size() > 1) {
                                ratingRegion.setAverageResultPercentSecondPlace(generalRatingDistinctRegionInfo.getDistinctPercentResult().get(1));
                            }
                            if (generalRatingDistinctRegionInfo.getDistinctPercentResult().size() > 2) {
                                ratingRegion.setAverageResultPercentThirdPlace(generalRatingDistinctRegionInfo.getDistinctPercentResult().get(2));
                            }
                            if (generalRatingDistinctRegionInfo.getDistinctPercentResult().size() > 3) {
                                ratingRegion.setAverageResultPercentLastPlace(generalRatingDistinctRegionInfo.getDistinctPercentResult()
                                        .get(generalRatingDistinctRegionInfo.getDistinctPercentResult().size() - 1));
                                ratingRegion.setPlaceGeneralRatingLastPlace(generalRatingDistinctRegionInfo.getDistinctPercentResult().size());
                            }
                            if (generalRatingDistinctRegionInfo.getDistinctPercentResult().size() > 4) {
                                ratingRegion.setAverageResultPercentPenultimatePlace(generalRatingDistinctRegionInfo.getDistinctPercentResult()
                                        .get(generalRatingDistinctRegionInfo.getDistinctPercentResult().size() - 2));
                                ratingRegion.setPlaceGeneralRatingPenultimatePlace(generalRatingDistinctRegionInfo.getDistinctPercentResult().size() - 1);
                            }
                            ratingRegion.setAllPlaces(generalRatingDistinctRegionInfo.getDistinctPercentResult());
                        }
                        generalRating.setGeneralRatingRegion(ratingRegion);

                        generalSchoolRatingDTO.stream().filter(x -> x.getLearningYear().equals(learningYear))
                                .filter(x -> x.getDiagnosticSubjectName().equals(name)).forEach(schoolRating -> {

                                    if (schoolRatingEnabled) {
                                        generalRating.setGeneralRatingSchool(
                                                buildRating(false, schoolId, null, generalDistinctPercentSchool, allPercentsSchool, learningYear, name, schoolRating));
                                    }

                                    generalRating.setGeneralRatingClass(
                                            buildRating(true, null, personIds, generalDistinctPercentClass, allPercentsClass, learningYear, name, schoolRating));

                                });
                        generalRatings.add(generalRating);
                    });

            responseRating.setGeneralRating(generalRatings);
            generalRatingResponse.add(responseRating);
        }

        return generalRatingResponse;
    }

    private GeneralRatingResponse.GeneralRatingEntity buildRating(Boolean byClass, String schoolId, List<String> personIds,
                                                                  List<GeneralRatingDistinctInfo> generalDistinctPercent,
                                                                  List<CountPercentsBySubjects> allPercents, String learningYear, String name,
                                                                  GeneralDiagnosticRatingDTO rating) {


        GeneralRatingDistinctInfo generalRatingDistinctSchoolInfo = generalDistinctPercent.stream()
                .filter(a -> a.getLearningYear().equals(learningYear))
                .filter(a -> a.getDiagnosticSubjectName().equals(name))
                .findFirst().orElse(null);
        if (Objects.isNull(generalRatingDistinctSchoolInfo)) {
            return null;
        }

        List<Double> distinctPercentResult = generalRatingDistinctSchoolInfo.getDistinctPercentResult()
                .stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        if (!distinctPercentResult.contains(rating.getAveragePercentExecutionResult())) {
            distinctPercentResult.add(rating.getAveragePercentExecutionResult());
            distinctPercentResult = distinctPercentResult.stream().distinct()
                    .sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        }


        GeneralRatingResponse.GeneralRatingEntity ratingSchool = new GeneralRatingResponse.GeneralRatingEntity();
        ratingSchool.setAverageResultPercentStudent(round(rating.getAveragePercentExecutionResult(), 1));

        CountPercentsBySubjects lowerPercents = null;

        if (bestOfEnabled) {
            if (byClass) {
                lowerPercents =
                        clickHouseRepository.searchCountLowerPercentsClass(name, rating.getAveragePercentExecutionResult(), personIds)
                                .stream().filter(x -> x.getLearningYear().equals(learningYear))
                                .findFirst().orElse(null);
            } else {
                lowerPercents =
                        clickHouseRepository.searchCountLowerPercentsSchool(name, rating.getAveragePercentExecutionResult(), schoolId)
                                .stream().filter(x -> x.getLearningYear().equals(learningYear))
                                .findFirst().orElse(null);
            }
            CountPercentsBySubjects countPercentsBySubjectSchool = allPercents.stream().filter(x -> x.getLearningYear().equals(learningYear))
                    .filter(x -> x.getDiagnosticSubjectName().equals(name)).findFirst().orElse(null);
            if (Objects.nonNull(countPercentsBySubjectSchool) && Objects.nonNull(schoolId) && !schoolId.equals(rating.getSchoolId())) {
                countPercentsBySubjectSchool.setCountPercents(countPercentsBySubjectSchool.getCountPercents() + 1);
            }

            if (Objects.nonNull(countPercentsBySubjectSchool) && Objects.nonNull(lowerPercents)) {
                ratingSchool.setPercentLowerOthers(round(100 / countPercentsBySubjectSchool.getCountPercents() * lowerPercents.getCountPercents(), 1));
            } else {
                ratingSchool.setPercentLowerOthers(0.0);
            }
        } else {
            ratingSchool.setPercentLowerOthers(0.0);
        }

        if (Objects.nonNull(generalRatingDistinctSchoolInfo)) {
            ratingSchool.setPlaceGeneralRatingStudent(distinctPercentResult
                    .indexOf(rating.getAveragePercentExecutionResult()) + 1);
            ratingSchool.setAverageResultPercentFirstPlace(distinctPercentResult.get(0));
            if (distinctPercentResult.size() > 1) {
                ratingSchool.setAverageResultPercentSecondPlace(distinctPercentResult.get(1));
            }
            if (distinctPercentResult.size() > 2) {
                ratingSchool.setAverageResultPercentThirdPlace(distinctPercentResult.get(2));
            }
            if (distinctPercentResult.size() > 3) {
                ratingSchool.setAverageResultPercentLastPlace(distinctPercentResult
                        .get(distinctPercentResult.size() - 1));
                ratingSchool.setPlaceGeneralRatingLastPlace(distinctPercentResult.size());
            }
            if (distinctPercentResult.size() > 4) {
                ratingSchool.setAverageResultPercentPenultimatePlace(distinctPercentResult
                        .get(distinctPercentResult.size() - 2));
                ratingSchool.setPlaceGeneralRatingPenultimatePlace(distinctPercentResult.size() - 1);
            }
            ratingSchool.setAllPlaces(distinctPercentResult);
        }
        return ratingSchool;
    }
}
