package ru.portfolio.ax.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Iterables;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.reflect.ConstructorUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;
import org.springframework.data.util.CastUtils;
import org.springframework.data.util.DirectFieldAccessFallbackBeanWrapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.NumberUtils;
import ru.portfolio.ax.configuration.RepositoryRestConfiguration;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.common.*;
import ru.portfolio.ax.repository.*;
import ru.portfolio.ax.repository.common.GenericProjection;
import ru.portfolio.ax.repository.ref.RefRepository;
import ru.portfolio.ax.repository.ref.interest.DeletableRefRepository;
import ru.portfolio.ax.rest.dto.PersonAuthPermission;
import ru.portfolio.ax.rest.dto.ReachableDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.util.Utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.prependIfMissing;

@Service
@Transactional
@SuppressWarnings("all")
@RequiredArgsConstructor
public class CrudService {
    @Getter
    private final ObjectMapper objectMapper;
    @Getter
    private final JdbcTemplate jdbcTemplate;

    private Map<Class, RefRepository> refRepositoryMap;
    private Map<Class, DeletableRefRepository> deletableRefRepositoryMap;
    private Map<Class, EntityRepository> entityRepositoryMap;
    private Map<Class, PersonallyEntityRepository> personallyEntityRepositoryMap;
    private final RepositoryRestConfiguration restConfiguration;
    private final ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
    private final LinkedObjectRepository linkedObjectRepository;

    public static final ExampleMatcher MATCHER = ExampleMatcher.matching()
            .withIgnoreCase().withStringMatcher(ExampleMatcher.StringMatcher.CONTAINING);
    public static final ExampleMatcher EXACT_MATCHER = ExampleMatcher.matching()
            .withIgnoreCase().withStringMatcher(ExampleMatcher.StringMatcher.EXACT);
    public static final ExampleMatcher EXACT_CASE_MATCHER = ExampleMatcher.matching()
            .withStringMatcher(ExampleMatcher.StringMatcher.EXACT);

    @Autowired
    public void setEntityRepositoryMap(List<? extends EntityRepository> repositories) {
        entityRepositoryMap = repositories.stream().collect(Collectors.toMap(EntityRepository::getClazz, Function.identity()));
    }

    @Autowired
    public void setPersonallyEntityRepositoryMap(List<? extends PersonallyEntityRepository> repositories) {
        personallyEntityRepositoryMap = repositories.stream().collect(Collectors.toMap(PersonallyEntityRepository::getClazz, Function.identity()));
    }

    @Autowired
    public void setRefRepositoryMap(List<? extends RefRepository> repositories) {
        refRepositoryMap = repositories.stream().collect(Collectors.toMap(RefRepository::getClazz, Function.identity()));
    }

    @Autowired
    public void setDeletableRefRepositoryMap(List<? extends DeletableRefRepository> repositories) {
        deletableRefRepositoryMap = repositories.stream().collect(Collectors.toMap(DeletableRefRepository::getClazz, Function.identity()));
    }

    private AbstractRepository getRepo(Class<? extends AbstractEntity> clazz) {
        return entityRepositoryMap.get(clazz);
    }

    private AbstractRefRepository getRefRepo(Class<? extends AbstractRefEntity> clazz) {
        return refRepositoryMap.get(clazz);
    }

    private DeletableRefRepository getDeletableRefRepo(Class<? extends AbstractRefEntity> clazz) {
        return deletableRefRepositoryMap.get(clazz);
    }

    private PersonallyEntityRepository getPersonallyEntityRepo(Class<? extends PersonallyEntity> clazz) {
        return personallyEntityRepositoryMap.get(clazz);
    }

    public <E extends AbstractEntity<Long>> E create(E object, Class<E> clazz) {
        return clazz.cast(entityRepositoryMap.get(clazz).save(object));
    }


    public <E extends AbstractEntity<Long>> E excelCreate(E object) {
        if (isNull(object)) return null;
        Class<? extends AbstractEntity> clazz = object.getClass();
        return (E) entityRepositoryMap.get(clazz).save(object);
    }

    //@Secured(byPerson = false)
    public <E extends AbstractEntity<Long>> E create(E object) {
        Class<? extends AbstractEntity> clazz = object.getClass();
        E saved = (E) entityRepositoryMap.get(clazz).save(object);
        if (Linkable.class.isAssignableFrom(clazz)) {
            createLinkedObjects((Linkable) saved);
        }
        return saved;
    }

    //@Secured(byPerson = false)
    public <E extends AbstractEntity<Long>> E update(E object) {
        EntityRepository<E> abstractRepository = entityRepositoryMap.get(object.getClass());
        E origin = abstractRepository.findById(object.getId())
                .orElseThrow(PortfolioException.notFound(object.getClass()));
        return abstractRepository.save(origin);
    }

    @SneakyThrows
    public <E extends AbstractEntity<Long>> E update(Long id, E object, Class<E> clazz) {
        EntityRepository<E> abstractRepository = entityRepositoryMap.get(clazz);
        E origin = abstractRepository.findById(id)
                .orElseThrow(PortfolioException.notFound(clazz));
        Utils.copyNonNullProperties(object, origin, "id");
        return abstractRepository.save(origin);
    }

    @SneakyThrows
    public <E extends AbstractEntity<Long>> E put(Long id, E object, Class<E> clazz) {
        EntityRepository<E> abstractRepository = entityRepositoryMap.get(clazz);
        E origin = abstractRepository.findById(id)
                .orElseThrow(PortfolioException.notFound(clazz));
        object.setId(id);
        if (Linkable.class.isAssignableFrom(clazz)) {
            updateLinkedObjects((Linkable) object);
        }
        return abstractRepository.save(object);
    }

    public <E extends AbstractEntity<Long>> void delete(Long id, Class<E> clazz) {
        entityRepositoryMap.get(clazz).deleteById(id);
    }

    public <E extends AbstractEntity<Long>> void deleteAll(List<E> objects) {
        if (objects.isEmpty()) return;
        Class<? extends AbstractEntity> aClass = objects.iterator().next().getClass();
        entityRepositoryMap.get(aClass).deleteAll(objects);
    }

    @SneakyThrows
    public <ID extends Number, E extends AbstractEntity<ID>> E find(ID id, Class<E> clazz) {
        return clazz.cast(getRepo(clazz).findById(id).orElseThrow(PortfolioException.notFound(clazz)));
    }

    @SneakyThrows
    public <E extends AbstractEntity<Long> & Linkable> E findLinkable(Long id, Class<E> clazz) {
        return clazz.cast(getRepo(clazz).findById(id).orElse(null));
    }

    @ReadOnly
    @Deprecated // переиспользовать на exist + not in id
    @SneakyThrows
    public <E extends AbstractEntity<Long>> List<E> findPersByHash(String hashCode, Class<E> clazz) {
        Preconditions.checkArgument(PersonallyEntity.class.isAssignableFrom(clazz), "Сущность не содержит md5 хеш");
        PersonallyEntityRepository repo = PersonallyEntityRepository.class.cast(getRepo(clazz));
        return repo.findAllByHashCode(hashCode);
    }

    @ReadOnly
    @SneakyThrows
    public <E extends AbstractEntity<Long>> List<Job> findJobByHash(String hashCode, Class<E> clazz) {
        Preconditions.checkArgument(Job.class.isAssignableFrom(clazz), "Сущность не содержит md5 хеш");
        JobRepository repo = JobRepository.class.cast(getRepo(clazz));
        return repo.findAllByHashCode(hashCode);
    }

    @ReadOnly
    @SneakyThrows
    public <E extends AbstractEntity<Long>> List<Document> findDocumentByHash(String hashCode, Class<E> clazz) {
        Preconditions.checkArgument(Document.class.isAssignableFrom(clazz), "Сущность не содержит md5 хеш");
        DocumentRepository repo = DocumentRepository.class.cast(getRepo(clazz));
        return repo.findAllByHashCode(hashCode);
    }

    @SneakyThrows
    public <E extends AbstractEntity<Long>> boolean existPersByHash(String hashCode, Class<E> clazz) {
        Preconditions.checkArgument(PersonallyEntity.class.isAssignableFrom(clazz), "Сущность не содержит md5 хеш");
        PersonallyEntityRepository repo = PersonallyEntityRepository.class.cast(getRepo(clazz));
        return repo.existsByHashCode(hashCode);
    }

    @SneakyThrows
    public <E extends AbstractEntity<Long>> boolean existPersByHashIsDeletePersonId
            (String hashCode, Class<E> clazz, Boolean isDeleted, String personId) {
        Preconditions.checkArgument(PersonallyEntity.class.isAssignableFrom(clazz), "Сущность не содержит md5 хеш");
        PersonallyEntityRepository repo = PersonallyEntityRepository.class.cast(getRepo(clazz));
        return repo.existsByHashCodeAndIsDeleteAndPersonId(hashCode, isDeleted, personId);
    }

    @ReadOnly
    @SneakyThrows
    @Cacheable("ref")
    public <ID extends Object, E extends AbstractRefEntity<ID>> E findRef(ID id, Class<E> clazz) {
        return clazz.cast(getRefRepo(clazz).findById(id).orElseThrow(PortfolioException.refNotFound(id, clazz)));
    }

    @ReadOnly
    @SneakyThrows
    public <E extends RefEntity> E find(Integer id, Class<E> clazz) {
        return clazz.cast(refRepositoryMap.get(clazz).findById(id).orElseThrow(PortfolioException.refNotFound(clazz)));
    }

    @ReadOnly
    public <E extends AbstractEntity<Long>> E findFirst(Class<E> clazz) {//todo fixit
        Page<E> all = entityRepositoryMap.get(clazz).findAll(PageRequest.of(0, 1));
        return Iterables.getFirst(all.getContent(), null);
    }

    @ReadOnly
    public <E> Optional<E> findOptional(Long id, Class<E> clazz) {
        return entityRepositoryMap.get(clazz).findById(id);
    }

    @ReadOnly
    public <E> Optional<E> findOptional(Integer id, Class<E> clazz) {
        return refRepositoryMap.get(clazz).findById(id);
    }

    @ReadOnly
    @Deprecated
    public <E extends AbstractEntity<Long>> List<E> findAll(Class<E> clazz) {
        return entityRepositoryMap.get(clazz).findAll();
    }

    @ReadOnly
    @SneakyThrows
    public <E extends AbstractEntity<Long>, ID extends Object> List<E> findAll(String clazz, Set<ID> ids) {
        return CastUtils.cast(findAll(ids, CastUtils.cast(Class.forName(
                prependIfMissing(clazz, "ru.portfolio.ax.model.")))));
    }

    @ReadOnly
    public <E extends AbstractEntity<Long>> Page<E> findAll(Class<E> clazz, Pageable pageable) {
        return getRepo(clazz).findAll(pageable);
    }

    @ReadOnly
    public <ID extends Object, E extends RefEntity> E findFirstRef(Class<E> clazz, Integer code) {
        return Optional.ofNullable(code)
                .flatMap(v -> findFirstRef(clazz, RefEntity.Fields.code, v))
                .orElseGet(() -> RefEntity.build(clazz, code));
    }

    @ReadOnly
    public <ID extends Object, E extends RefEntity> E findFirstRefByValue(Class<E> clazz, String value) {
        return Optional.ofNullable(value)
                .flatMap(v -> findFirstRef(clazz, RefEntity.Fields.value, v))
                .orElseGet(() -> RefEntity.buildByValue(clazz, value));
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractEntity<ID>> Optional<E> findFirst(Class<E> clazz, String key, Object value) {
        return findFirst(clazz, Collections.singletonMap(key, value));
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractRefEntity<ID>> Optional<E> findFirstRef(Class<E> clazz, String key, Object value) {
        return findFirstRef(clazz, Collections.singletonMap(key, value));
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractEntity<ID>> Optional<E> findFirst(Class<E> clazz, Map<String, ?> fields) {
        return getRepo(clazz).findOne(Example.of(objectMapper.convertValue(fields, clazz), MATCHER));
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractRefEntity<ID>> Optional<E> findFirstRef(Class<E> clazz, Map<String, ?> fields) {
        return getRefRepo(clazz).findOne(Example.of(objectMapper.convertValue(fields, clazz), EXACT_MATCHER));
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractEntity<ID>> Page<E> findAll(Class<E> clazz, Map<String, ?> fields, Pageable pageable) {
        return getRepo(clazz).findAll(Example.of(objectMapper.convertValue(fields, clazz), MATCHER), pageable);
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractEntity<ID>> List<E> findAll(Class<E> clazz, Map<String, ?> fields) {
        return getRepo(clazz).findAll(Example.of(objectMapper.convertValue(fields, clazz), EXACT_CASE_MATCHER));
    }


    @Cacheable("allRepo")
    public <ID extends Object, E extends AbstractRefEntity<ID>> Page<E> findAllRepo(Class<E> clazz, Map<String, ?> fields, Pageable pageable) {
        return getRefRepo(clazz).findAll(Example.of(objectMapper.convertValue(fields, clazz), MATCHER), pageable);
    }

    public <ID extends Object, E extends AbstractRefEntity<ID>> void existAllRef(Set<ID> ids, Class<E> clazz) {
        if (CollectionUtils.isEmpty(ids)) return;
        List<GenericProjection<ID>> allById = getRefRepo(clazz).findAllByIdInAndIdIsNotNull(ids);
        Set<ID> extract = Utils.extract(allById, GenericProjection::getId);
        Set<ID> difference = Sets.symmetricDifference(ids, extract);
        if (!difference.isEmpty()) {//fixme
            throw PortfolioException.refNotFound(clazz).get();
        }
    }

    @ReadOnly
    public <ID extends Object, E extends AbstractEntity<ID>> List<E> findAll(Set<ID> ids, Class<E> clazz) {
        if (CollectionUtils.isEmpty(ids)) return Collections.emptyList();
        List<E> allById = getRepo(clazz).findAllById(ids);
        Set<ID> difference = Sets.symmetricDifference(ids, Utils.extract(allById, AbstractEntity::getId));
        if (!difference.isEmpty()) {//fixme
            throw PortfolioException.notFound(clazz).get();
        }
        return allById;
    }


    @Cacheable("allRefsByIds")
    public <ID extends Object, E extends AbstractRefEntity<ID>> List<E> findAllRefs(Set<ID> ids, Class<E> clazz) {
        if (CollectionUtils.isEmpty(ids)) return Collections.emptyList();
        return getRefRepo(clazz).findAllById(ids);
    }

    @ReadOnly
    @Cacheable("allRefs")
    public <E extends RefEntity> List<E> findAllRefs(Class<E> clazz) {
        return getRefRepo(clazz).findAll();
    }

    @ReadOnly
    @Cacheable("allDeletableRefs")
    public <E extends RefEntity> List<E> findAllDeletableRefs(Class<E> clazz, Boolean isArchive) {
        if (isNull(isArchive)) {
            return getDeletableRefRepo(clazz).findAll();
        }
        return getDeletableRefRepo(clazz).findAllByIsArchive(isArchive);
    }

    @ReadOnly
    @SneakyThrows
    public <E extends PersonallyEntity> E findPersonallyEntity(Long id, Class clazz) {
        return (E) personallyEntityRepositoryMap.get(clazz).findByIdIgnoreWhereAnnotation(id).orElseThrow(PortfolioException.notFound(clazz));
    }

    public <E extends AbstractEntity<? extends Object>, R extends AbstractRepository<? extends Object, E>> R getRepository(Class<E> clazz) {
        return CastUtils.cast(Objects.requireNonNull(entityRepositoryMap.get(clazz)));
    }

    public void clearFieldMap(Map<String, String> fields) {
        fields.remove(AbstractEntity.Fields.id);
        fields.remove(restConfiguration.getLimitParamName());
        fields.remove(restConfiguration.getPageParamName());
        fields.remove(restConfiguration.getSortParamName());
    }

    @SneakyThrows
    public <E extends AbstractEntity<Long>, T extends AbstractEntity<Long> &
            ReachableDTO & PersonAuthPermission> E convert(T object, Class<E> clazz, Long id, Boolean ignoreNullProperties) {
        BeanWrapper wrappedSrc = new DirectFieldAccessFallbackBeanWrapper(object);
        PropertyDescriptor[] descriptors = wrappedSrc.getPropertyDescriptors();

        for (PropertyDescriptor descriptor : descriptors) {
            Class<?> propertyType = descriptor.getPropertyType();
            if (!Utils.isSimple(propertyType)) {
                String name = descriptor.getName();

                Object value = object.getMap().remove(name + "Code");
                if (isNull(value)) continue;
                Integer t = NumberUtils.parseNumber(value.toString(), Integer.class);

                AbstractRefEntity<Integer> ref = findRef(t, CastUtils.cast(propertyType));
                wrappedSrc.setPropertyValue(name, ref);
            }
        }
        Preconditions.checkState(object.getMap().isEmpty(), "Поля не определены: " + object.getMap().keySet());
        ConstraintViolation<?> first;
        object.validate(this);

        if (Objects.nonNull(id)) {
            E old = find(id, clazz);
            if (ignoreNullProperties) {
                Utils.copyNonNullProperties(object, old, "id");
            } else {
                BeanUtils.copyProperties(object, old, "id");
            }
            first = Utils.first(validatorFactory.getValidator().validate(old));
            Preconditions.checkState(isNull(first), "Не все обязательные поля заполнены");
            return old;
        } else {
            first = Utils.first(validatorFactory.getValidator().validate(object));
            Preconditions.checkState(isNull(first), "Не все обязательные поля заполнены");
            E entity = ConstructorUtils.invokeConstructor(clazz);
            if (ignoreNullProperties) {
                Utils.copyNonNullProperties(object, entity, "id");
            } else {
                BeanUtils.copyProperties(object, entity, "id");
            }
            return entity;
        }
    }

    private void createLinkedObjects(Linkable entity) {
        if (Objects.isNull(entity.getLinkedObjects())) {
            return;
        }
        ((Linkable) entity).getLinkedObjects().forEach(x -> {
            // Если нет сущности для связи - пропускаем
            if (!checkLinkedExists(x.getEntityId(), x.getEntityType(), entity.getPersonId())) return;
            LinkedObject lo = new LinkedObject();
            lo.setDeleted(false);
            lo.setEntityId1(entity.getId());
            lo.setEntityId2(x.getEntityId());
            lo.setEntityType1(((Linkable) entity).getEntityTypeCode());
            lo.setEntityType2(x.getEntityType());
            linkedObjectRepository.save(lo);
        });

    }

    private void updateLinkedObjects(Linkable entity) {
        List<LinkedObject> existingLinkeds = linkedObjectRepository.findAllByEntityId(entity.getId());
        Set<Long> existingLinkedsIds = Stream.concat(existingLinkeds.stream().map(y -> y.getEntityId1()),
                existingLinkeds.stream().map(y -> y.getEntityId2()))
                .collect(Collectors.toSet());
        List<Long> actualLinkedsIds = new ArrayList<>();
        Optional.ofNullable(entity.getLinkedObjects()).ifPresent(linked ->
                linked.forEach(x -> {
                    if (existingLinkedsIds.contains(x.getEntityId())) {
                        actualLinkedsIds.add(x.getEntityId());
                        return;
                    }
                    if (!checkLinkedExists(x.getEntityId(), x.getEntityType(), entity.getPersonId())) return;
                    LinkedObject lo = new LinkedObject();
                    lo.setDeleted(false);
                    lo.setEntityId1(entity.getId());
                    lo.setEntityId2(x.getEntityId());
                    lo.setEntityType1(((Linkable) entity).getEntityTypeCode());
                    lo.setEntityType2(x.getEntityType());
                    linkedObjectRepository.save(lo);
                    actualLinkedsIds.add(x.getEntityId());
                }));

        existingLinkeds.stream().filter(x -> !actualLinkedsIds.contains(x.getEntityId1()) &&
                !actualLinkedsIds.contains(x.getEntityId2())).forEach(x -> {
            x.setDeleted(true);
            linkedObjectRepository.save(x);
        });
    }

    private <T extends AbstractEntity<Long>> boolean checkLinkedExists(Long id, Integer entityType, String personId) {
        Linkable link = null;
        if (entityType.equals(1)) {
            link = findLinkable(id, Event.class);
        } else if (entityType.equals(2)) {
            link = findLinkable(id, Employment.class);
        } else if (entityType.equals(3)) {
            link = findLinkable(id, Reward.class);
        } else if (entityType.equals(4)) {
            link = findLinkable(id, SportReward.class);
        } else if (entityType.equals(5)) {
            link = findLinkable(id, Project.class);
        } else if (entityType.equals(6)) {
            link = findLinkable(id, Affilation.class);
        } else if (entityType.equals(7)) {
            link = findLinkable(id, GIAWorldskills.class);
        } else if (entityType.equals(8)) {
            link = findLinkable(id, Job.class);
        } else if (entityType.equals(9)) {
            link = findLinkable(id, Document.class);
        } else if (entityType.equals(10)) {
            link = findLinkable(id, CheckInHistory.class);
        }
        if (Objects.isNull(link)) {
            return false;
        }
        if (!link.getPersonId().equals(personId)) {
            return false;
        }
        return true;
    }
}
