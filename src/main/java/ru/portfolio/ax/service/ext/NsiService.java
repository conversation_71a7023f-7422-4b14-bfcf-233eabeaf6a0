package ru.portfolio.ax.service.ext;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import ru.portfolio.ax.configuration.NsiProperty;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.NsiOrganization;
import ru.portfolio.ax.repository.NsiOrganizationRepository;
import ru.portfolio.ax.rest.dto.nsi.CriteriaDTO;
import ru.portfolio.ax.rest.dto.nsi.NsiDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.util.RestUtils;
import ru.portfolio.ax.util.Utils;

import java.util.*;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Service
public class NsiService {
    private final String app;
    private final String nsiUrl;
    private final String nsiKey;

    private final RestTemplate nsi;

    private final RestTemplate nsiExcelTemplate;

    @Value("${nsi.staffId}")
    private Integer staffId;
    @Value("${nsi.orgId}")
    private Integer nsiOrgCatalogId;

    @Value("${mesh-contingent.oldContingentCategoryId}")
    private Integer oldContingentId;

    private NsiOrganizationRepository nsiOrganizationRepository;

    public NsiService(RestTemplate nsiRestTemplate,
                      RestTemplate nsiExcelRestTemplate,
                      NsiProperty nsiProperty,
                      NsiOrganizationRepository nsiOrganizationRepository) {
        this.nsiUrl = nsiProperty.getHost();
        this.app = nsiProperty.getApp();
        this.nsiKey = nsiProperty.getKey();
        this.nsi = nsiRestTemplate;
        this.nsiExcelTemplate = nsiExcelRestTemplate;
        this.nsiOrganizationRepository = nsiOrganizationRepository;
    }

    public Optional<NsiDTO.Response> getEmployee(String creatorId) {
        return getName(CriteriaDTO.builder().id(staffId).criteria(creatorId).build());
    }

    public List<NsiDTO.Response> getEmployee(Collection<String> creatorId) {
        if (CollectionUtils.isEmpty(creatorId)) return Collections.emptyList();
        return getNames(CriteriaDTO.builder().id(staffId).criteriaIn(creatorId).build());
    }

    public List<NsiDTO.Response> getEmployers(Collection<String> creatorId) {
        if (CollectionUtils.isEmpty(creatorId)) return Collections.emptyList();
        return getNames(CriteriaDTO.builder()
                .id(staffId)
                .criteriaIn(creatorId)
                .projection("surname", "first_name", "second_name")
                .build());
    }

    public List<NsiDTO.Response> getEmployersWithGlobalId(Collection<String> creatorId) {
        if (CollectionUtils.isEmpty(creatorId)) return Collections.emptyList();
        return getNamesOrEmptyList(CriteriaDTO.builder()
                .id(staffId)
                .criteriaIn(creatorId)
                .projection("surname", "first_name", "second_name", "global_id")
                .build());
    }

    public Optional<NsiDTO.Response> getOrgName(@Nullable Integer orgId) {
        if (Objects.isNull(orgId)) return Optional.empty();
        NsiOrganization nsiOrganization = nsiOrganizationRepository.findByGlobalId(Long.valueOf(orgId));
        if (Objects.isNull(nsiOrganization)) return Optional.empty();
        NsiDTO.Response response = mapNsiOrganizationToNsiDtoResponse(nsiOrganization);
        return Optional.of(response);
    }

    public List<NsiDTO.Response> getOrgNames(List<Long> ids) {
        List<NsiOrganization> nsiOrganizations = nsiOrganizationRepository.findAllByGlobalIdIn(ids);
        List<NsiDTO.Response> response = new ArrayList<>();
        for (NsiOrganization nsiOrganization : nsiOrganizations) {
            NsiDTO.Response row = mapNsiOrganizationToNsiDtoResponse(nsiOrganization);
            response.add(row);
        }
        return response;
    }

    public List<NsiDTO.Response> getOrgNames() {
        List<NsiOrganization> nsiOrganizations =
                nsiOrganizationRepository.findAllByType2IdKeyInAndStatusIdKeyAndConstituentEntityKey(Lists.newArrayList("1", "3", "5"),
                        "1", "31927468");
        List<NsiDTO.Response> response = new ArrayList<>();
        for (NsiOrganization nsiOrganization : nsiOrganizations) {
            NsiDTO.Response row = mapNsiOrganizationToNsiDtoResponse(nsiOrganization);
            response.add(row);
        }
        return response;
    }

    @ReadOnly
    @Cacheable(
            value = "getOrgNameFromNsiService",
            key = "#orgId",
            condition = "#orgId != null",
            cacheManager = "caffeineCacheManager"
    )
    public Optional<NsiDTO.Response> getOrgNameFromNsiService(Integer orgId) {
        CriteriaDTO criteriaDTO = CriteriaDTO.builder().id(nsiOrgCatalogId)
                .customCriteria("global_id = " + orgId.toString())
                .build();
        ResponseEntity<NsiDTO> httpEntity = nsi.exchange(UriComponentsBuilder.fromHttpUrl(nsiUrl).build().toUri(), HttpMethod.POST,
                RestUtils.createHeaderEntity(criteriaDTO, app, nsiKey), NsiDTO.class);
        return Optional.ofNullable(Utils.first(httpEntity.getBody().getResponse()));
    }

    private NsiDTO.Response mapNsiOrganizationToNsiDtoResponse(NsiOrganization nsiOrganization) {
        NsiDTO.Response response = new NsiDTO.Response();
        response.setGlobalId(nsiOrganization.getGlobalId());
        response.setShortName(nsiOrganization.getShortName());
        response.setFullName(nsiOrganization.getFullName());
        NsiDTO.EoDistrictId eoDistrictId = new NsiDTO.EoDistrictId();
        eoDistrictId.setKey(NumberUtils.createInteger(nsiOrganization.getEoDistrictId()));
        response.setEoDistrictId(Lists.newArrayList(eoDistrictId));
        return response;
    }

    @ReadOnly
    public List<NsiDTO.Response> getTeachers(Integer orgId, String subject, Integer limit) {
        //SP-4595
//        if (isNull(limit)) {
//            limit = 50;
//        }
        String customCriteria = "internal_mov.organization_id.global_id = " + orgId + " and " +
                "internal_mov.job_name = 'Учитель'";
        if (nonNull(subject)) {
            customCriteria += " and subjects_taught.subject = '" + subject + "'";
        }
        return getResponseWithNullHandling(CriteriaDTO.builder().id(staffId)
                .customCriteria(customCriteria)
                .limit(limit)
                .build());
    }

    @ReadOnly
    public Optional<NsiDTO.Response> getName(CriteriaDTO criteriaDTO) {
        return Optional.ofNullable(Utils.first(getNames(criteriaDTO)));
    }

    @ReadOnly
    public Optional<NsiDTO.Response> getName(List<Long> globalIds) {
        List<NsiOrganization> organizations = nsiOrganizationRepository.findAllByGlobalIdIn(globalIds);
        if (organizations.isEmpty()) {
            return Optional.empty();
        }
        NsiDTO.Response response = new NsiDTO.Response();
        NsiOrganization organization = Utils.first(organizations);
        response.setShortName(organization.getShortName());
        response.setFullName(organization.getFullName());
        response.setGlobalId(organization.getGlobalId());
        response.setEoId(organization.getEoId());
        return Optional.ofNullable(response);
    }

    @ReadOnly
    public List<NsiDTO.Response> getNames(CriteriaDTO criteriaDTO) {
        ResponseEntity<NsiDTO> httpEntity = nsi.exchange(UriComponentsBuilder.fromHttpUrl(nsiUrl).build().toUri(), HttpMethod.POST,
                RestUtils.createHeaderEntity(criteriaDTO, app, nsiKey), NsiDTO.class);
        PortfolioException.check(!isNull(httpEntity.getBody()) && !isNull(httpEntity.getBody().getResponse()), PortfolioException.get422());
        return httpEntity.getBody().getResponse();
    }

    @ReadOnly
    public List<NsiDTO.Response> getNamesOrEmptyList(CriteriaDTO criteriaDTO) {
        ResponseEntity<NsiDTO> httpEntity = nsi.exchange(UriComponentsBuilder.fromHttpUrl(nsiUrl).build().toUri(), HttpMethod.POST,
                RestUtils.createHeaderEntity(criteriaDTO, app, nsiKey), NsiDTO.class);
        return nonNull(httpEntity.getBody().getResponse()) ? httpEntity.getBody().getResponse() : new ArrayList<>();
    }

    @ReadOnly
    public List<NsiDTO.Response> getNames(List<Long> globalIds) {
        List<NsiOrganization> organizations = nsiOrganizationRepository.findAllByGlobalIdIn(globalIds);
        List<NsiDTO.Response> response = new ArrayList<>();
        for (NsiOrganization organization : organizations) {
            NsiDTO.Response dto = new NsiDTO.Response();
            dto.setShortName(organization.getShortName());
            dto.setFullName(organization.getFullName());
            dto.setGlobalId(organization.getGlobalId());
            dto.setEoId(organization.getEoId());
            response.add(dto);
        }
        return response;
    }

    @ReadOnly
    public List<NsiDTO.Response> getResponseWithNullHandling(CriteriaDTO criteriaDTO) {
        ResponseEntity<NsiDTO> httpEntity = nsi.exchange(UriComponentsBuilder.fromHttpUrl(nsiUrl).build().toUri(), HttpMethod.POST,
                RestUtils.createHeaderEntity(criteriaDTO, app, nsiKey), NsiDTO.class);
        PortfolioException.check(!isNull(httpEntity.getBody()) && !isNull(httpEntity.getBody().getResponse())
                && httpEntity.getBody().getStatus().equals("0"), PortfolioException.get493());
        PortfolioException.check(!isNull(httpEntity.getBody()) && !isNull(httpEntity.getBody().getResponse()), PortfolioException.get422());
        return httpEntity.getBody().getResponse();
    }

    @ReadOnly
    public List<NsiDTO.Response> getResponse(CriteriaDTO criteriaDTO) {
        ResponseEntity<NsiDTO> httpEntity = nsi.exchange(UriComponentsBuilder.fromHttpUrl(nsiUrl).build().toUri(), HttpMethod.POST,
                RestUtils.createHeaderEntity(criteriaDTO, app, nsiKey), NsiDTO.class);
        if (isNull(httpEntity.getBody()) || isNull(httpEntity.getBody().getResponse())) return new ArrayList<>();
        return httpEntity.getBody().getResponse();
    }

    @ReadOnly
    public List<NsiDTO.Response> getOrganizationId(String name) {
        List<NsiOrganization> nsiOrganizations = nsiOrganizationRepository.findAllByName(name);
        if (nsiOrganizations.isEmpty()) return new ArrayList<>();
        List<NsiDTO.Response> result = new ArrayList<>();
        for (NsiOrganization nsiOrganization : nsiOrganizations) {
            NsiDTO.Response row = new NsiDTO.Response();
            row.setGlobalId(nsiOrganization.getGlobalId());
            row.setFullName(nsiOrganization.getFullName());
            row.setShortName(nsiOrganization.getShortName());
            result.add(row);
        }
        return result;
    }

    @ReadOnly
    public List<NsiDTO.Response> getByFio(String first, String surname, String second) {
        return getResponse(CriteriaDTO.builder().id(staffId)
                .fio(first, surname, second)
                .projection("first_name", "surname", "second_name", "global_id")
                .build());
    }
}
