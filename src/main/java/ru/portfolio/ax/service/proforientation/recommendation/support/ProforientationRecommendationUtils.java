package ru.portfolio.ax.service.proforientation.recommendation.support;

import lombok.NonNull;
import org.springframework.lang.Nullable;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.service.proforientation.recommendation.dtos.AfishaEventIndustryDto;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ProforientationRecommendationUtils {
    /**
     * Сортируем события по возрастанию даты и времени начала
     */
    public static Comparator<Map.Entry<AfishaEvents, Set<SpecialitiesByIndustry>>> getAfishaEventsComparator() {
        return Map.Entry.comparingByKey(
                Comparator.comparing(
                        AfishaEvents::getStartAt,
                        Comparator.nullsLast(Comparator.naturalOrder()
                        )
                ));
    }

    /**
     * Ограничиваем количество событий
     */
    public static Map<AfishaEvents, Set<SpecialitiesByIndustry>> limitAfishaEvents(@NonNull Map<AfishaEvents, Set<SpecialitiesByIndustry>> result,
                                                                                   int limit) {
        return result.entrySet().stream()
                .sorted(getAfishaEventsComparator())
                .limit(limit)
                .collect(
                        Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (x1, x2) -> x1
                        )
                );
    }

    /**
     * Объединяем мапы в result
     */
    public static void unionAfishaEventsMap(@NonNull Map<AfishaEvents, Set<SpecialitiesByIndustry>> result,
                                            @NonNull Map<AfishaEvents, Set<SpecialitiesByIndustry>> mapForAdd) {
        mapForAdd.forEach((afishaEvent, specialitiesByIndustriesForAdd) -> {
            Set<SpecialitiesByIndustry> specialitiesByIndustriesResult = result.getOrDefault(afishaEvent, new HashSet<>());
            specialitiesByIndustriesResult.addAll(specialitiesByIndustriesForAdd);

            result.put(afishaEvent, specialitiesByIndustriesResult);
        });
    }

    /**
     * Получаем отрасль для события
     * У одного события может быть несоклько отраслей (???)
     * Берем первую отрасль
     */
    @Nullable
    public static AfishaEventIndustryDto getAfishaEventIndustryDto(Set<SpecialitiesByIndustry> specialitiesByIndustry) {
        SpecialitiesByIndustry specialityByIndustry = specialitiesByIndustry.stream()
                .findAny()
                .orElse(null);

        return getAfishaEventIndustryDto(specialityByIndustry);
    }

    private static AfishaEventIndustryDto getAfishaEventIndustryDto(SpecialitiesByIndustry specialityByIndustry) {
        if (specialityByIndustry == null) {
            return null;
        }

        return AfishaEventIndustryDto.builder()
                .eventId(specialityByIndustry.getEventId())
                .industryCode(specialityByIndustry.getCodeIndustry())
                .industryName(specialityByIndustry.getNameIndustry())
                .build();
    }

    @Nullable
    public static String getIndustryCode(AfishaEventIndustryDto afishaEventIndustryDto) {
        return afishaEventIndustryDto != null
                ? afishaEventIndustryDto.getIndustryCode()
                : null;
    }

    @Nullable
    public static String getIndustryName(AfishaEventIndustryDto afishaEventIndustryDto) {
        return afishaEventIndustryDto != null
                ? afishaEventIndustryDto.getIndustryName()
                : null;
    }
}
