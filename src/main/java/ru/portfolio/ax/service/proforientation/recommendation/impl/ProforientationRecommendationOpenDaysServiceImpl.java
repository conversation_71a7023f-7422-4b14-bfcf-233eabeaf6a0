package ru.portfolio.ax.service.proforientation.recommendation.impl;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.model.dto.CollegeDTO;
import ru.portfolio.ax.repository.AfishaEventsRepository;
import ru.portfolio.ax.repository.SpecialitiesByIndustryRepository;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationOpenDaysService;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationRecommendationUtils;
import ru.portfolio.ax.service.proforientation.recommendation.support.openDays.OpenDaysMapper;
import ru.portfolio.ax.service.proforientation.recommendation.support.openDays.OpenDaysValidation;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.util.CollectionUtils.isEmpty;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProforientationRecommendationOpenDaysServiceImpl implements ProforientationRecommendationOpenDaysService {
    AfishaEventsRepository afishaEventsRepository;
    SpecialitiesByIndustryRepository specialitiesByIndustryRepository;

    int EVENT_TYPE = 41; // Код типа "День открытых дверей"
    int EVENTS_LIMIT = 20;

    @Override
    public List<GetRecommendationProforientationResponse.RecommendDod> getData(@NonNull LearnerCategoryEnum learnerCategory,
                                                                               @NonNull List<String> alreadyRegisteredEventIds,
                                                                               JsonNode recommendations) {
        if (!OpenDaysValidation.validate(learnerCategory)) {
            return Collections.emptyList();
        }

        List<GetRecommendationProforientationResponse.RecommendDod> resultAfishaEvents = isStudentHaveRecommendations(recommendations)
                ? getDataWithRecommendations(alreadyRegisteredEventIds, recommendations)
                : getDataWithoutRecommendations(alreadyRegisteredEventIds);

        return ProforientationRecommendationRecommendationUtils.sliceData(resultAfishaEvents, EVENTS_LIMIT);
    }

    /* Если у ученика есть рекомендации */
    private boolean isStudentHaveRecommendations(JsonNode recommendations) {
        return recommendations != null
                && !isEmpty(ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations))
                && (!isEmpty(ProforientationRecommendationRecommendationUtils.getCollegeNames(recommendations))
                || !isEmpty(ProforientationRecommendationRecommendationUtils.getSpecialityCodes(recommendations, null))
        );
    }

    private List<GetRecommendationProforientationResponse.RecommendDod> getDataWithRecommendations(List<String> alreadyRegisteredEventIds,
                                                                                      JsonNode recommendations) {
        List<String> excludedEventIds = new ArrayList<>(alreadyRegisteredEventIds);
        List<GetRecommendationProforientationResponse.RecommendDod> resultAfishaEvents = new ArrayList<>();

        Set<String> industryCodes = ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations);
        if (industryCodes.isEmpty()) {
            return Collections.emptyList();
        }

        Set<CollegeDTO> colleges = ProforientationRecommendationRecommendationUtils.getCollegesByIndustryCodes(recommendations, null);
        Set<String> specialityCodes = ProforientationRecommendationRecommendationUtils.getSpecialityCodes(recommendations, null);

        if (!colleges.isEmpty() && !specialityCodes.isEmpty()) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByCollegeNamesAndSpecialityCodes = getAfishaEventsByCollegesAndSpecialityCodes(
                    excludedEventIds,
                    industryCodes,
                    colleges,
                    specialityCodes
            );
            resultAfishaEvents.addAll(OpenDaysMapper.execute(afishaEventsByCollegeNamesAndSpecialityCodes));
            ProforientationRecommendationRecommendationUtils.extendEventIdsByAfishaMap(
                    excludedEventIds,
                    afishaEventsByCollegeNamesAndSpecialityCodes
            );
        }

        if (resultAfishaEvents.size() < EVENTS_LIMIT && !specialityCodes.isEmpty()) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsBySpecialityCodes = getAfishaEventsBySpecialityCodes(
                    excludedEventIds,
                    industryCodes,
                    specialityCodes
            );
            resultAfishaEvents.addAll(OpenDaysMapper.execute(afishaEventsBySpecialityCodes));
            ProforientationRecommendationRecommendationUtils.extendEventIdsByAfishaMap(
                    excludedEventIds,
                    afishaEventsBySpecialityCodes
            );
        }

        if (resultAfishaEvents.size() < EVENTS_LIMIT) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByIndustryCodes = getAfishaEventsByIndustryCodes(
                    excludedEventIds,
                    industryCodes
            );
            resultAfishaEvents.addAll(OpenDaysMapper.execute(afishaEventsByIndustryCodes));
        }

        return resultAfishaEvents;
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByCollegesAndSpecialityCodes(List<String> excludedEventIds,
                                                                                                       Set<String> industryCodes,
                                                                                                       Set<CollegeDTO> colleges,
                                                                                                       Set<String> specialityCodes) {
        Set<String> collegeNames = colleges.stream().map(CollegeDTO::getName).collect(Collectors.toSet());
        Set<String> collegeCodes = colleges.stream().map(CollegeDTO::getOrganizationId).collect(Collectors.toSet());

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                excludedEventIds,
                industryCodes,
                collegeNames,
                collegeCodes,
                specialityCodes
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsBySpecialityCodes(List<String> excludedEventIds,
                                                                                            Set<String> industryCodes,
                                                                                            Set<String> specialityCodes) {
        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                excludedEventIds,
                industryCodes,
                null,
                null,
                specialityCodes
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByIndustryCodes(List<String> excludedEventIds,
                                                                                          Set<String> industryCodes) {

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                excludedEventIds,
                industryCodes,
                null,
                null,
                null
        );
    }

    private List<GetRecommendationProforientationResponse.RecommendDod> getDataWithoutRecommendations(List<String> alreadyRegisteredEventIds) {
        Set<String> industryCodes = specialitiesByIndustryRepository.findByNotArchivePrioritySpecialitiesRef().stream()
                .map(SpecialitiesByIndustry::getCodeIndustry)
                .collect(Collectors.toSet());
        if (industryCodes.isEmpty()) {
            return Collections.emptyList();
        }

        Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByIndustryCodes = getAfishaEventsByIndustryCodes(
                alreadyRegisteredEventIds,
                industryCodes
        );

        return OpenDaysMapper.execute(afishaEventsByIndustryCodes);
    }
}
