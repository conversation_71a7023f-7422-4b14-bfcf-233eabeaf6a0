package ru.portfolio.ax.service.proforientation.recommendation.impl;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.repository.AfishaEventsRepository;
import ru.portfolio.ax.repository.SpecialitiesByIndustryRepository;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationOpenDaysService;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationRecommendationUtils;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationUtils;
import ru.portfolio.ax.service.proforientation.recommendation.support.openDays.OpenDaysMapper;
import ru.portfolio.ax.service.proforientation.recommendation.support.openDays.OpenDaysValidation;

import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.util.CollectionUtils.isEmpty;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProforientationRecommendationOpenDaysServiceImpl implements ProforientationRecommendationOpenDaysService {
    AfishaEventsRepository afishaEventsRepository;
    SpecialitiesByIndustryRepository specialitiesByIndustryRepository;

    int EVENT_TYPE = 41; // Код типа "День открытых дверей"
    int EVENTS_LIMIT = 20;

    @Override
    public List<GetRecommendationProforientationResponse.RecommendDod> getData(@NonNull LearnerCategoryEnum learnerCategory,
                                                                               @NonNull List<String> alreadyRegisteredEventIds,
                                                                               JsonNode recommendations) {
        if (!OpenDaysValidation.validate(learnerCategory)) {
            return Collections.emptyList();
        }

        Map<AfishaEvents, Set<SpecialitiesByIndustry>> resultAfishaEvents = isStudentHaveRecommendations(recommendations)
                ? getDataWithRecommendations(alreadyRegisteredEventIds, recommendations)
                : getDataWithoutRecommendations(alreadyRegisteredEventIds);

        return OpenDaysMapper.execute(
                ProforientationRecommendationUtils.limitAfishaEvents(
                        resultAfishaEvents,
                        EVENTS_LIMIT
                )
        );
    }

    /* Если у ученика есть рекомендации */
    private boolean isStudentHaveRecommendations(JsonNode recommendations) {
        return recommendations != null
                && !isEmpty(ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations))
                && (!isEmpty(ProforientationRecommendationRecommendationUtils.getCollegeNames(recommendations))
                || !isEmpty(ProforientationRecommendationRecommendationUtils.getSpecialityCodes(recommendations, null))
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getDataWithRecommendations(List<String> alreadyRegisteredEventIds,
                                                                                      JsonNode recommendations) {
        Map<AfishaEvents, Set<SpecialitiesByIndustry>> resultAfishaEvents = new HashMap<>();

        Set<String> industryCodes = ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations);
        if (industryCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByCollegeNamesAndSpecialityCodes = getAfishaEventsByCollegeNamesAndSpecialityCodes(
                alreadyRegisteredEventIds,
                industryCodes,
                recommendations
        );
        ProforientationRecommendationUtils.unionAfishaEventsMap(resultAfishaEvents, afishaEventsByCollegeNamesAndSpecialityCodes);

        if (resultAfishaEvents.keySet().size() < EVENTS_LIMIT) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsBySpecialityCodes = getAfishaEventsBySpecialityCodes(
                    alreadyRegisteredEventIds,
                    industryCodes,
                    recommendations
            );
            ProforientationRecommendationUtils.unionAfishaEventsMap(resultAfishaEvents, afishaEventsBySpecialityCodes);
        }

        if (resultAfishaEvents.keySet().size() < EVENTS_LIMIT) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByIndustryCodes = getAfishaEventsByIndustryCodes(
                    alreadyRegisteredEventIds,
                    industryCodes
            );
            ProforientationRecommendationUtils.unionAfishaEventsMap(resultAfishaEvents, afishaEventsByIndustryCodes);
        }

        return resultAfishaEvents;
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByCollegeNamesAndSpecialityCodes(List<String> alreadyRegisteredEventIds,
                                                                                                           Set<String> industryCodes,
                                                                                                           JsonNode recommendations) {
        Set<String> collegeNames = ProforientationRecommendationRecommendationUtils.getCollegeNames(recommendations);
        Set<String> specialityCodes = ProforientationRecommendationRecommendationUtils.getSpecialityCodes(recommendations, null);

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                alreadyRegisteredEventIds,
                industryCodes,
                collegeNames,
                null,
                specialityCodes
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsBySpecialityCodes(List<String> alreadyRegisteredEventIds,
                                                                                            Set<String> industryCodes,
                                                                                            JsonNode recommendations) {
        Set<String> specialityCodes = ProforientationRecommendationRecommendationUtils.getSpecialityCodes(recommendations, null);

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                alreadyRegisteredEventIds,
                industryCodes,
                null,
                null,
                specialityCodes
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByIndustryCodes(List<String> alreadyRegisteredEventIds,
                                                                                          Set<String> industryCodes) {

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                alreadyRegisteredEventIds,
                industryCodes,
                null,
                null,
                null
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getDataWithoutRecommendations(List<String> alreadyRegisteredEventIds) {
        HashMap<AfishaEvents, Set<SpecialitiesByIndustry>> resultAfishaEvents = new HashMap<>();

        Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByIndustryCodes = getAfishaEventsByIndustryCodes(
                alreadyRegisteredEventIds
        );
        ProforientationRecommendationUtils.unionAfishaEventsMap(resultAfishaEvents, afishaEventsByIndustryCodes);

        return resultAfishaEvents;
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByIndustryCodes(List<String> alreadyRegisteredEventIds) {
        Set<String> industryCodes = specialitiesByIndustryRepository.findByNotArchivePrioritySpecialitiesRef().stream()
                .map(SpecialitiesByIndustry::getCodeIndustry)
                .collect(Collectors.toSet());

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                alreadyRegisteredEventIds,
                industryCodes,
                null,
                null,
                null
        );
    }
}
