package ru.portfolio.ax.service.proforientation.recommendation.impl;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.model.dto.CollegeDTO;
import ru.portfolio.ax.repository.AfishaEventsRepository;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationProfessionalTestsService;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationRecommendationUtils;
import ru.portfolio.ax.service.proforientation.recommendation.support.professionalTests.ProfessionalTestsMapper;
import ru.portfolio.ax.service.proforientation.recommendation.support.professionalTests.ProfessionalTestsValidation;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Math.min;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProforientationRecommendationProfessionalTestsServiceImpl implements ProforientationRecommendationProfessionalTestsService {
    AfishaEventsRepository afishaEventsRepository;

    int EVENT_TYPE = 43; // Код типа "Профессиональная проба"
    int EVENTS_LIMIT = 20;

    @Override
    public List<GetRecommendationProforientationResponse.RecommendationProfTest> getData(@NonNull LearnerCategoryEnum learnerCategory,
                                                                                         @NonNull List<String> alreadyRegisteredEventIds,
                                                                                         JsonNode recommendations) {
        if (!ProfessionalTestsValidation.validate(learnerCategory, recommendations)) {
            return Collections.emptyList();
        }

        List<String> excludedEventIds = new ArrayList<>(alreadyRegisteredEventIds);
        List<GetRecommendationProforientationResponse.RecommendationProfTest> resultAfishaEvents = new ArrayList<>();

        Set<String> industryCodesWithoutExcluded = getIndustryCodeWithoutExcluded(recommendations);
        if (industryCodesWithoutExcluded.isEmpty()) {
            return Collections.emptyList();
        }

        Set<CollegeDTO> colleges = ProforientationRecommendationRecommendationUtils.getCollegesByIndustryCodes(recommendations, industryCodesWithoutExcluded);
        Set<String> specialityCodes = ProforientationRecommendationRecommendationUtils.getSpecialityCodes(recommendations, industryCodesWithoutExcluded);

        if (!colleges.isEmpty() && !specialityCodes.isEmpty()) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByCollegeNamesAndSpecialityCodes = getAfishaEventsByCollegesAndSpecialityCodes(
                    excludedEventIds,
                    industryCodesWithoutExcluded,
                    colleges,
                    specialityCodes
            );
            resultAfishaEvents.addAll(ProfessionalTestsMapper.execute(afishaEventsByCollegeNamesAndSpecialityCodes));
            ProforientationRecommendationRecommendationUtils.extendEventIdsByAfishaMap(
                    excludedEventIds,
                    afishaEventsByCollegeNamesAndSpecialityCodes
            );
        }

        if (resultAfishaEvents.size() < EVENTS_LIMIT && !specialityCodes.isEmpty()) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsBySpecialityCodes = getAfishaEventsBySpecialityCodes(
                    excludedEventIds,
                    industryCodesWithoutExcluded,
                    specialityCodes
            );
            resultAfishaEvents.addAll(ProfessionalTestsMapper.execute(afishaEventsBySpecialityCodes));
            ProforientationRecommendationRecommendationUtils.extendEventIdsByAfishaMap(
                    excludedEventIds,
                    afishaEventsBySpecialityCodes
            );
        }

        if (resultAfishaEvents.size() < EVENTS_LIMIT) {
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByIndustryCodes = getAfishaEventsByIndustryCodes(
                    excludedEventIds,
                    industryCodesWithoutExcluded
            );
            resultAfishaEvents.addAll(ProfessionalTestsMapper.execute(afishaEventsByIndustryCodes));
        }

        return ProforientationRecommendationRecommendationUtils.sliceData(resultAfishaEvents, EVENTS_LIMIT);
    }

    private Set<String> getIndustryCodeWithoutExcluded(JsonNode recommendations) {
        Set<String> industryCodes = ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations);
        Set<String> excludedIndustryCodes = ProforientationRecommendationRecommendationUtils.getExcludedIndustryCodes(recommendations);

        industryCodes.removeAll(excludedIndustryCodes);

        return industryCodes;
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByCollegesAndSpecialityCodes(List<String> excludedEventIds,
                                                                                                       Set<String> industryCodes,
                                                                                                       Set<CollegeDTO> colleges,
                                                                                                       Set<String> specialityCodes) {
        Set<String> collegeNames = colleges.stream().map(CollegeDTO::getName).collect(Collectors.toSet());
        Set<String> collegeCodes = colleges.stream().map(CollegeDTO::getOrganizationId).collect(Collectors.toSet());

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                excludedEventIds,
                industryCodes,
                collegeNames,
                collegeCodes,
                specialityCodes
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsBySpecialityCodes(List<String> excludedEventIds,
                                                                                            Set<String> industryCodes,
                                                                                            Set<String> specialityCodes) {
        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                excludedEventIds,
                industryCodes,
                null,
                null,
                specialityCodes
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByIndustryCodes(List<String> excludedEventIds,
                                                                                          Set<String> industryCodes) {
        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                excludedEventIds,
                industryCodes,
                null,
                null,
                null
        );
    }
}
