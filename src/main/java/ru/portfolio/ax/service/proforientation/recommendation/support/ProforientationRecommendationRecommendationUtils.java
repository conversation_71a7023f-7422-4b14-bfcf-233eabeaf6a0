package ru.portfolio.ax.service.proforientation.recommendation.support;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.NonNull;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.model.dto.CollegeDTO;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.lang.Math.min;
import static ru.portfolio.ax.util.Utils.safeGet;

/**
 * Сервис работы с /proforientation/crm/v1
 * <p>
 * Описание: <a href="https://wiki.mos.social/pages/viewpage.action?pageId=643173861">...</a>
 */
public class ProforientationRecommendationRecommendationUtils {

    /**
     * Список названий рекомендованных колледжей
     */
    public static Set<String> getCollegeNames(JsonNode recommendations) {
        Set<String> collegeNames = new HashSet<>();

        JsonNode industries = safeGet(() -> recommendations.get("data").get("industry").get("data"));
        if (industries != null) {
            industries.forEach(industry -> {
                JsonNode colleges = safeGet(() -> industry.get("colleges").get("college"));

                if (colleges != null) {
                    colleges.forEach(college -> {
                        String collegeName = safeGet(college.get("name"), JsonNode::asText);

                        if (collegeName != null) {
                            collegeNames.add(collegeName);
                        }
                    });
                }
            });
        }

        return collegeNames;
    }

    public static Set<CollegeDTO> getCollegesByIndustryCodes(
            @NonNull JsonNode recommendations,
            Set<String> industryCodes
    ) {
        Set<CollegeDTO> collegesByIndustryCodes = new HashSet<>();
        JsonNode industries = safeGet(() -> recommendations.get("data").get("industry").get("data"));

        if (industries != null) {
            industries.forEach(industry -> {
                String industryID = safeGet(industry.get("industryID"), JsonNode::asText);

                if (industryCodes == null || (industryID != null && industryCodes.contains(industryID))) {
                    JsonNode colleges = safeGet(() -> industry.get("colleges").get("college"));

                    if (colleges != null) {
                        colleges.forEach(college -> {
                            String name = safeGet(college.get("name"), JsonNode::asText);
                            String organizationId = safeGet(college.get("organization_id"), JsonNode::asText);

                            if (name != null && organizationId != null) {
                                CollegeDTO collegeDTO = new CollegeDTO();

                                collegeDTO.setName(name);
                                collegeDTO.setOrganizationId(organizationId);

                                collegesByIndustryCodes.add(collegeDTO);
                            }
                        });
                    }
                }
            });
        }

        return collegesByIndustryCodes;
    }

    /**
     * Список кодов рекомендованных специальностей
     */
    public static Set<String> getSpecialityCodes(@NonNull JsonNode recommendations, Set<String> industryCodes) {
        Set<String> specsCodes = new HashSet<>();

        JsonNode industries = safeGet(() -> recommendations.get("data").get("industry").get("data"));
        if (industries != null) {
            industries.forEach(industry -> {
                String industryID = safeGet(industry.get("industryID"), JsonNode::asText);

                if (industryCodes == null || (industryID != null && industryCodes.contains(industryID))) {
                    JsonNode specs = safeGet(() -> industry.get("specs").get("spec"));

                    if (specs != null) {
                        specs.forEach(spec -> {
                            String specsCode = safeGet(spec.get("code"), JsonNode::asText);

                            if (specsCode != null) {
                                specsCodes.add(spec.get("code").asText());
                            }
                        });
                    }
                }
            });
        }

        return specsCodes;
    }

    /**
     * Рекомендованные отрасли для ученика (Маршрутный лист)
     */
    public static Set<String> getIndustryCodes(JsonNode recommendations) {
        Set<String> industryIds = new HashSet<>();

        JsonNode roadmap = safeGet(() -> recommendations.get("data").get("roadmap").get("data"));

        if (roadmap != null) {
            roadmap.forEach(roadmapElem -> {
                String industryID = safeGet(roadmapElem.get("industryID"), JsonNode::asText);

                if (industryID != null) {
                    industryIds.add(industryID);
                }
            });
        }

        return industryIds;
    }

    /**
     * Получаем отрасли прошедших посещенных мероприятий ученика
     */
    public static Set<String> getVisitedIndustryIds(JsonNode recommendations) {
        Set<String> industryIds = new HashSet<>();

        ProforientationRecommendationRecommendationUtils.getHistoryEvents(recommendations).elements()
                .forEachRemaining(event -> {
                    Boolean isEventVisited = safeGet(event.get("visited"), JsonNode::asBoolean);
                    String industryID = safeGet(event.get("industryID"), JsonNode::asText);

                    if (industryID != null
                            && Boolean.TRUE.equals(isEventVisited)) {
                        industryIds.add(industryID);
                    }
                });

        return industryIds;
    }

    /**
     * Список записей на прошедшие мероприятия
     */
    private static JsonNode getHistoryEvents(JsonNode recommendations) {
        return safeGet(() -> recommendations.get("data").get("events").get("history").get("data").get("masterstvo"));
    }

    /**
     * Получаем количество посещенных проб мероприятий
     */
    public static int getVisitedEventsCount(JsonNode recommendations) {
        AtomicInteger count = new AtomicInteger(0);

        ProforientationRecommendationRecommendationUtils.getHistoryEvents(recommendations).elements()
                .forEachRemaining(event -> {
                    Boolean isEventVisited = safeGet(event.get("visited"), JsonNode::asBoolean);

                    if (Boolean.TRUE.equals(isEventVisited)) {
                        count.incrementAndGet();
                    }
                });

        return count.get();
    }

    /**
     * Получаем отрасли прошедших посещенных мероприятий ученика
     */
    public static Set<String> getRegisteredIndustryIds(JsonNode recommendations) {
        Set<String> industryIds = new HashSet<>();

        JsonNode registrationEvents = safeGet(() -> recommendations.get("data").get("events").get("registration").get("data").get("masterstvo"));

        if (registrationEvents != null) {
            registrationEvents.elements()
                    .forEachRemaining(event -> {
                        String industryID = safeGet(event.get("industryID"), JsonNode::asText);

                        if (industryID != null) {
                            industryIds.add(industryID);
                        }
                    });
        }

        return industryIds;
    }

    /**
     * Получаем исключенные отрасли
     */
    public static Set<String> getExcludedIndustryCodes(JsonNode recommendations) {
        Set<String> excludedIndustryIds = new HashSet<>();

        // Исключаем из рекомендованных отраслей те, мероприятия которых ученик уже посещал
        excludedIndustryIds.addAll(ProforientationRecommendationRecommendationUtils.getVisitedIndustryIds(recommendations));

        // Исключаем из рекомендованных отраслей те, на мероприятия которых ученик уже зарегистрирован
        excludedIndustryIds.addAll(ProforientationRecommendationRecommendationUtils.getRegisteredIndustryIds(recommendations));

        return excludedIndustryIds;
    }

    public static void extendEventIdsByAfishaMap(
            List<String> eventIds,
            Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByCollegeNamesAndSpecialityCodes
    ) {
        eventIds.addAll(afishaEventsByCollegeNamesAndSpecialityCodes.keySet().stream().map(
                key -> key.getEventId()
        ).collect(Collectors.toList()));
    }

    public static <T> List<T> sliceData(List<T> data, int limit) {
        return data.subList(0, min(data.size(), limit));
    }
}
