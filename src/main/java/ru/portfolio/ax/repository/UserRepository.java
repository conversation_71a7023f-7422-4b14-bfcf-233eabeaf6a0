package ru.portfolio.ax.repository;

import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.User;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends EntityRepository<User> {

    Optional<User> findFirstByAupdId(Long aupdId);

    List<User> findByAupdIdEquals(Long aupd_id);

    List<User> findAllByChildrenContains(String childrenId);

    @Override
    default Class<User> getClazz() {
        return User.class;
    }
}
