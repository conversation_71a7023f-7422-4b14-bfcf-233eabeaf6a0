package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.Query;
import ru.portfolio.ax.model.ActionHistory;

import java.time.LocalDateTime;
import java.util.List;

public interface ActionHistoryRepository extends EntityRepository<ActionHistory> {
    @Override
    default Class<ActionHistory> getClazz() {
        return ActionHistory.class;
    }

    @Query(nativeQuery = true, value = "select * from portfolio.action_history " +
            "where action_date_time between :startDate and :endDate " +
            "and action_kind_code in(:actionKindCode) ")
    List<ActionHistory> findAllByActionDateTimeBetweenAndActionKindCodeIn(LocalDateTime startDate, LocalDateTime endDate, List<Integer> actionKindCode);
}