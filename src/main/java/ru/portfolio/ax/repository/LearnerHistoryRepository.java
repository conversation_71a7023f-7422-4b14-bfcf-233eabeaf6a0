package ru.portfolio.ax.repository;

import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.LearnerHistory;

import java.util.List;

public interface LearnerHistoryRepository extends EntityRepository<LearnerHistory> {
    @Override
    default Class<LearnerHistory> getClazz() {
        return LearnerHistory.class;
    }

    @ReadOnly
    List<LearnerHistory> findAllByPersonId(String personId);

}

