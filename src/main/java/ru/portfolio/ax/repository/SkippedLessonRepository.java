package ru.portfolio.ax.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import ru.portfolio.ax.model.SkippedLesson;

import java.util.List;
import java.util.Optional;

@Repository
public interface SkippedLessonRepository extends JpaRepository<SkippedLesson, Long> {

    List<SkippedLesson> findAllByPersonIdAndLessonIdAndIsDelete(String personId, Long lessonId, Boolean isDelete);

    Optional<SkippedLesson> findFirstByPersonIdAndLessonIdAndIsDelete(String personId, Long lessonId, Boolean isDelete);

    List<SkippedLesson> findAllByPersonIdAndLessonThemeIdAndIsDelete(String personId, Integer themeId, Boolean isDelete);
}
