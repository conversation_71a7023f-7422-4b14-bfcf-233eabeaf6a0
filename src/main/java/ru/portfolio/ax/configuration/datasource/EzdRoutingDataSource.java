package ru.portfolio.ax.configuration.datasource;

import com.google.common.base.Preconditions;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class EzdRoutingDataSource extends AbstractRoutingDataSource {
    private static ThreadLocal<Integer> currentReplication = new InheritableThreadLocal<>();

    private DataSourceSettings dataSourceSettings;
    private Map<Integer, DataSource> dataSources;
    private List<Integer> shardDataSourcesKeys = new ArrayList<>();
    private WeightedRandom random;
    private DataSourceReplicaProperties property;

    public EzdRoutingDataSource(DataSourceSettings dataSourceSettings, DataSourceReplicaProperties property) {
        this.dataSourceSettings = dataSourceSettings;
        initialize(property);
    }

    private void initialize(DataSourceReplicaProperties property) {
        dataSources = new HashMap<>();
        setTargetDataSources(Collections.unmodifiableMap(dataSources));

        random = new WeightedRandom();

        int indexDs = 1;
        Preconditions.checkState(dataSourceSettings.isValid(property));
        DataSource masterDS = dataSourceSettings.buildDataSource();
        setDefaultTargetDataSource(masterDS);
        random.addValue(indexDs, dataSourceSettings.getWeight());
        dataSources.put(indexDs++, masterDS);
        if (dataSourceSettings.getShard()) {
            shardDataSourcesKeys.add(indexDs);
        }

        for (DataSourceSettings replicaSettings : dataSourceSettings.getReplicas()) {
            Preconditions.checkState(replicaSettings.isValid(null));
            random.addValue(indexDs, replicaSettings.getWeight());
            dataSources.put(indexDs++, replicaSettings.buildDataSource());
            if (replicaSettings.getShard()) {
                shardDataSourcesKeys.add(indexDs);
            }
        }
    }

    public Map<Integer, DataSource> getDataSources() {
        return dataSources;
    }

    public List<Integer> getShardDataSourcesKeys() {
        return shardDataSourcesKeys;
    }

    public static void setReadOnly(Boolean useReadOnly) {
        ReadOnlyHolder.set(useReadOnly);
    }

    public static boolean isReadOnly() {
        return BooleanUtils.isTrue(ReadOnlyHolder.get());
    }

    @Override
    protected Object determineCurrentLookupKey() {
        if (BooleanUtils.isTrue(ReadOnlyHolder.get())) {
            Integer chosen = random.randomValue();
            currentReplication.set(chosen);
            return chosen;
        }

        //return null means using only master database
        return null;
    }

    public static Integer getCurrentReplica() {
        return currentReplication.get();
    }

    public WeightedRandom getRandom() {
        return random;
    }
}
