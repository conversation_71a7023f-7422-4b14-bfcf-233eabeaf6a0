package ru.portfolio.ax.rest;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import ru.portfolio.ax.service.ext.ClickHouseRedirectService;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.service.ClickHouseService;

@RestController
@RequiredArgsConstructor
@RequestMapping("/click-house")
public class ClickHouseController {
    private final ClickHouseService service;
    private final ClickHouseRedirectService redirectService;

    @GetMapping
    public PositiveResponse<Object> select(@RequestParam(defaultValue = "learner_portfolio_kafka_queue") String table) {
        return PortfolioApi.positiveResponse(service.select(table));
    }

    @GetMapping("swap")
    public PositiveResponse<Object> swap() {
        return PortfolioApi.positiveResponse(redirectService.swap());
    }
}
