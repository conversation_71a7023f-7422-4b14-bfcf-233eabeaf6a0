package ru.portfolio.ax.rest;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.common.AbstractEntity;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.util.aspect.LoggedMethod;
import ru.portfolio.ax.util.security.AuthComponent;
import ru.portfolio.ax.util.security.AuthComponentNew;
import ru.portfolio.ax.util.security.Secured;
import ru.portfolio.ax.util.security.SecuredNew;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;

import static ru.portfolio.ax.model.common.AbstractEntity.Fields.id;

@Validated
@RequiredArgsConstructor
public class ReadableController<ID extends Number, E extends AbstractEntity<ID>> {
    @Getter
    private final Class<E> clazz;
    protected final CrudService crudService;

    @Value("${old.auth.enabled}")
    boolean oldAuthEnabled;
    @Setter(onMethod = @__({@Autowired}))
    private AuthComponentNew authComponentNew;
    @Setter(onMethod = @__({@Autowired}))
    private AuthComponent authComponent;

    @ReadOnly
    @LoggedMethod
    @GetMapping
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", dataType = "integer", paramType = "query",
                    value = "Results page you want to retrieve (0..N)", defaultValue = "0"),
            @ApiImplicitParam(name = "size", dataType = "integer", paramType = "query",
                    value = "Number of records per page.", defaultValue = "5"),
            @ApiImplicitParam(name = "sort", allowMultiple = true, dataType = "string", paramType = "query",
                    value = "Sorting criteria in the format: property(,asc|desc). " +
                            "Default sort order is ascending. " +
                            "Multiple sort criteria are supported.")
    })
    public PositiveResponse<Iterable<E>> getAll(@SortDefault(sort = id, direction = Sort.Direction.DESC)
                                                @PageableDefault Pageable pageable, HttpServletRequest request,
                                                @RequestParam(required = false) Set<ID> ids,
                                                @RequestParam(required = false) Map<String, String> map) {
        BiFunction<Map<String, String>, Pageable, Iterable<E>> getter;
        crudService.clearFieldMap(map);


        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getAll", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.TEACHER, SecuredNew.GlobalRole.ADMIN_O_O)),
                    "getAll", null);
        }

        if (CollectionUtils.isNotEmpty(ids)) {
            getter = (s, p) -> crudService.findAll(ids, clazz);
        } else {
            getter = (s, p) -> crudService.findAll(clazz, map, pageable);
        }
        return PortfolioApi.positiveResponse(customizeFindAll(getter, request, map, pageable));
    }

    @ReadOnly
    @LoggedMethod
    @GetMapping("{id}")
    public PositiveResponse<E> getById(@PathVariable ID id, HttpServletRequest request) {
        E data = crudService.find(id, clazz);
        data.reachTransient(crudService);
        return PortfolioApi.positiveResponse(data);
    }

    protected <I extends Iterable<E>> I customizeFindAll(I objects, HttpServletRequest request) {
        return objects;
    }

    protected <I extends Iterable<E>> I customizeFindAll(BiFunction<Map<String, String>, Pageable, I> getter,
                                                         HttpServletRequest request,
                                                         Map<String, String> map,
                                                         Pageable pageable) {
        return customizeFindAll(getter.apply(map, pageable), request);
    }
}

