package ru.portfolio.ax.rest.kafka;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.portfolio.ax.configuration.datasource.aspect.WriteOnly;
import ru.portfolio.ax.model.Event;
import ru.portfolio.ax.model.KafkaMessageProcessed;
import ru.portfolio.ax.model.KafkaReaderLog;
import ru.portfolio.ax.model.Reward;
import ru.portfolio.ax.model.common.AbstractEntity;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.repository.EventRepository;
import ru.portfolio.ax.repository.KafkaMessageProcessedRepository;
import ru.portfolio.ax.repository.KafkaReaderLogRepository;
import ru.portfolio.ax.repository.RewardRepository;
import ru.portfolio.ax.repository.ref.*;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.util.Utils;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaConsumer {

    @Value("${kafka.education.group-id}")
    private String groupId;
    @Value("${kafka.education.topic}")
    private String topicName;
    @Value("${kafka.consumer.enabled}")
    private Boolean enabled;

    private final ObjectMapper objectMapper;
    private final EventRepository eventRepository;
    private final RewardRepository rewardRepository;

    private final OlympiadTypeRepository olympiadTypeRepository;
    private final OlympiadFormatRepository olympiadFormatRepository;
    private final DataSourceRefRepository dataSourceRefRepository;
    private final SectionRefRepository sectionRefRepository;
    private final RewardKindRefRepository rewardKindRefRepository;
    private final StatusRewardRefRepository statusRewardRefRepository;

    private final KafkaReaderLogRepository kafkaReaderLogRepository;
    private final KafkaMessageProcessedRepository kafkaMessageProcessedRepository;

    private final CrudService crudService;

    @WriteOnly
    @SneakyThrows
    @KafkaListener(groupId = "${kafka.education.group-id}", topics = "${kafka.education.topic}",
            containerFactory = "kafkaListenerContainerFactory", autoStartup = "${kafka.education.auto-startup}")
    public void listen(@Payload String in, @Header(KafkaHeaders.OFFSET) Long offset, @Header(KafkaHeaders.RECEIVED_MESSAGE_KEY) String key) {
        if (!enabled) {
            log.info("Reading education is disabled!");
            return;
        }
        KafkaMessageProcessed message = new KafkaMessageProcessed();
        ObjectMapper mapper = new ObjectMapper();
        JsonNode json = mapper.readTree(in);

        message.setKafkaOffset(offset);
        message.setKafkaKey(key);
        String hash = DigestUtils.md5Hex(in);
        message.setMd5Hash(hash);
        message.setMessage(json);

        List<KafkaMessageProcessed> allByMd5Hash = kafkaMessageProcessedRepository.findAllByMd5Hash(hash);
        KafkaMessageProcessed original =
                allByMd5Hash.stream().filter(x -> isNull(x.getDuplicateMessageId())).findFirst().orElse(null);
        if (Objects.nonNull(original)) {
            message.setDuplicateMessageId(original.getId());
            kafkaMessageProcessedRepository.save(message);
            kafkaLogFail("Найдено сообщение с идентичным payload: id = " + original.getId(), null, message);
            return;
        }
        if (!allByMd5Hash.isEmpty()) {
            Long lowestId = allByMd5Hash.stream().min(Comparator.comparing(AbstractEntity::getId)).get().getId();
            message.setDuplicateMessageId(lowestId);
            kafkaMessageProcessedRepository.save(message);
            kafkaLogFail("Найдено сообщение с идентичным payload: id = " + lowestId, null, message);
            return;
        }
        kafkaMessageProcessedRepository.save(message);
        EducationMessageDTO educationMessageDTO;
        try {
            educationMessageDTO = objectMapper.readValue(in, EducationMessageDTO.class);
        } catch (Exception ex) {
            kafkaLogFail("Ошибка при парсинге сообщения", null, message);
            return;
        }

        if (Objects.isNull(educationMessageDTO)) {
            kafkaLogFail("Сообщение равно NULL", null, message);
            return;
        }
        String personId = educationMessageDTO.getPersonId();

        boolean isFullUpdate = (Objects.nonNull(educationMessageDTO.getMessageType())
                && educationMessageDTO.getMessageType().equals("fullProfile"))
                || (Objects.nonNull(educationMessageDTO.getActions())
                && educationMessageDTO.getActions().stream().anyMatch(x -> x.getAction().equals("deleteAll")));

        List<Event> allFromEducation =
                eventRepository.findAllByPersonIdAndSourceCodeAndIsDelete(personId, 15, false);

        educationMessageDTO.getActions().forEach(x -> {
            List<String> nullableRequiredFields = getNullRequiredFields(x);
            if (!nullableRequiredFields.isEmpty()) {
                kafkaLogFail("Отсутствуют обязательные поля: " + nullableRequiredFields.toString(), personId, message);
                return;
            }
            if (x.getAction().equals("createOrUpdate")) {
                List<Event> existingEvents = allFromEducation.stream()
                        .filter(e -> e.getContestId().equals(x.getContestId()))
                        .collect(Collectors.toList());
//                List<Event> existingEvents = eventRepository.findAllByPersonIdAndContestIdAndSourceCode(
//                        personId, x.getContestId(), 15);
                if (existingEvents.size() > 1) {
                    String errorMessage = "Найдено более одной записи с person_id = " + personId
                            + " и contest_id = " + x.getContestId();
                    kafkaLogFail(errorMessage, personId, message);
                    return;
                }
                if (existingEvents.isEmpty()) {
                    createOrUpdate(personId, x, null, message);
                } else {
                    createOrUpdate(personId, x, existingEvents.get(0), message);
                    allFromEducation.remove(existingEvents.get(0));
                }
            }
//            else if (x.getAction().equals("deleteAll")) {
//                deleteAll(personId, message);
//
//            }
        });

        if (isFullUpdate) {
            deleteEducations(allFromEducation, personId, message);
        }
    }

    private void deleteEducations(List<Event> existingEvents, String personId, KafkaMessageProcessed message) {
        existingEvents.forEach(x -> {
            x.setIsDelete(true);

            String hashCode = Utils.createHash(x);
            x.reach(crudService);
            x.setHashCode(hashCode);
            kafkaLogOk(x, 3, personId, message);

            List<Reward> rewardsByEntity = rewardRepository.findAllByEntityId(x.getId().toString());
            rewardsByEntity.forEach(r -> {
                r.setIsDelete(true);
                r.reach(crudService);
                String rewardHashCode = Utils.createHash(r);
                r.setHashCode(rewardHashCode);
                kafkaLogOk(r, 3, personId, message);
            });
            rewardRepository.saveAll(rewardsByEntity);
        });

        eventRepository.saveAll(existingEvents);
    }

    private void deleteAll(String personId, KafkaMessageProcessed message) {
        List<Event> existingEvents = eventRepository.findAllByPersonIdAndSourceCode(
                personId, 15);

        if (existingEvents.size() < 1) {
            String errorMessage = "Не найдено ни одной записи с person_id = " + personId;
            kafkaLogFail(errorMessage, personId, message);
            return;
        }
        deleteEducations(existingEvents, personId, message);
    }

    private void createOrUpdate(String personId, EducationMessageDTO.Action action, Event event, KafkaMessageProcessed message) {
        try {
            Integer actionKindCode;
            if (isNull(event)) {
                event = new Event();
                actionKindCode = 1;
            } else {
                actionKindCode = 2;
            }
            fillEvent(event, personId, action);

            event.reach(crudService);
            String hashCode = Utils.createHash(event);
            if (CollectionUtils.isNotEmpty(eventRepository.findAllByHashCode(hashCode))) {
                kafkaLogFail("Запись с такими значениями уже существует в event", personId, message);
                return;
            }
            event.setHashCode(hashCode);
            eventRepository.save(event);

            kafkaLogOk(event, actionKindCode, personId, message);

            if (Lists.newArrayList(1, 3).contains(action.getRewardType())) {
                Reward reward;
                if (actionKindCode.equals(1)) {
                    reward = new Reward();
                } else {
                    reward = rewardRepository.findByEntityIdAndEntityTypeFromKafka(event.getId().toString(), "event");
                    if (isNull(reward)) reward = new Reward();
                }
                fillReward(reward, event.getId(), personId, action);
                reward.reach(crudService);
                String rewardHashCode = Utils.createHash(reward);
                if (CollectionUtils.isNotEmpty(eventRepository.findAllByHashCode(rewardHashCode))) {
                    kafkaLogFail("Запись с такими значениями уже существует в reward", personId, message);
                    return;
                }
                reward.setHashCode(hashCode);
                rewardRepository.save(reward);
                event.setLinkedObjectIds(reward.getId().toString());
                eventRepository.save(event);
                kafkaLogOk(reward, actionKindCode, personId, message);
            } else {
                Reward reward =
                        rewardRepository.findByEntityIdAndEntityTypeFromKafka(event.getId().toString(), "event");
                if (nonNull(reward)) {
                    reward.setIsDelete(true);
                    reward.setEditDate(LocalDateTime.now());
                    rewardRepository.save(reward);
                }
            }
        } catch (Exception e) {
            kafkaLogFail("Ошибка при преобразовании action в объект", personId, message);
        }
    }

    private void fillEvent(Event event, String personId, EducationMessageDTO.Action action) {
        event.setPersonId(personId);
        event.setContestId(action.getContestId());
        event.setCategoryCode(1);
        event.setDataKind(7);
        event.setTypeCode(8L);
        event.setName(action.getTitle());
        event.setLevelEvent(olympiadTypeRepository.findById(action.getLevel() + 1).orElse(null));
        //event.setParallelsCode();
        event.setOrganizators("Учи.ру");
        String disciplineCode = StringUtils.join(action.getSubjects().stream()
                .map(EducationMessageDTO.Subjects::getId)
                .collect(Collectors.toList()), ", ");
        if (StringUtils.isNotEmpty(disciplineCode)) {
            event.setDisciplineCode(disciplineCode);
        }
        event.setStartDate(action.getContestStartedAt());
        event.setEndDate(action.getContestFinishedAt());
        event.setParticipatedFrom(action.getParticipatedFrom());
        event.setParticipatedTo(action.getParticipatedTo());
        event.setFormat(olympiadFormatRepository.findById(2).orElse(null));
        event.setResult(action.getScore().toString());
        event.setSource(dataSourceRefRepository.findById(15).orElse(null));
        event.setCreatorId("kafka");
        event.setIsImport(false);
        event.setIsDelete(false);
    }

    private void fillReward(Reward reward, Long eventId, String personId, EducationMessageDTO.Action action) {
        reward.setPersonId(personId);
        reward.setCategory(sectionRefRepository.findById(1).orElse(null));
        reward.setTypeCode(10L);
        Integer rewardKindCode = action.getRewardType().equals(1) ? 1 : 3;
        reward.setRewardType(rewardKindRefRepository.findById(rewardKindCode).orElse(null));
        reward.setStatusReward(statusRewardRefRepository.findById(1).orElse(null));
        reward.setLevelReward(olympiadTypeRepository.findById(action.getLevel() + 1).orElse(null));
        reward.setDate(ZonedDateTime.parse(action.getRewardPresentedAt()).toLocalDate());
        reward.setRewardNumber(action.getRewardId());
        String disciplineCode = StringUtils.join(action.getSubjects().stream()
                .map(EducationMessageDTO.Subjects::getId)
                .collect(Collectors.toList()), ", ");
        if (StringUtils.isNotEmpty(disciplineCode)) {
            reward.setDisciplineCode(disciplineCode);
        }
        reward.setEntityId(eventId.toString());
        reward.setEntityType("event");
        reward.setSource(dataSourceRefRepository.findById(15).orElse(null));
        reward.setCreatorId("kafka");
        reward.setIsImport(false);
        reward.setIsDelete(false);
        reward.setName(action.getRewardType().equals(1) ? "Победитель" : "Призер");
    }

    private <E extends PersonallyEntity> void kafkaLogOk(E entity, Integer actionKindCode, String personId, KafkaMessageProcessed message) {
        KafkaReaderLog log = new KafkaReaderLog();
        log.setPersonId(personId);
        log.setLogDateTime(LocalDateTime.now());
        log.setLogResult("OK");
        //log.setSourceCode();
        log.setActionTypeCode(7);
        log.setActionKindCode(actionKindCode);
        log.setEntityId(entity.getId());
        log.setEntityType(entity.getClass().getSimpleName().toLowerCase());
        log.setSourceCode(15);
        log.setMessageId(message.getId());
        kafkaReaderLogRepository.save(log);
    }

    private void kafkaLogFail(String errorMessage, String personId, KafkaMessageProcessed kafkaMessage) {
        KafkaReaderLog log = new KafkaReaderLog();
        log.setLogResult("FAIL");
        log.setErrorLogMessage(errorMessage);
        log.setPersonId(personId);
        log.setLogDateTime(LocalDateTime.now());
        log.setSourceCode(15);
        log.setMessageId(kafkaMessage.getId());
        kafkaReaderLogRepository.save(log);
    }

    public void delete(String personId, String contestId, KafkaMessageProcessed message) {
        List<Event> existingEvents = eventRepository.findAllByPersonIdAndContestIdAndSourceCode(
                personId, contestId, 15);
        if (existingEvents.size() == 1) {
            Event eventToDelete = existingEvents.iterator().next();
            eventToDelete.setIsDelete(true);
            eventRepository.save(eventToDelete);

            List<Reward> rewardsByEntity = rewardRepository.findAllByEntityId(eventToDelete.getId().toString());
            rewardsByEntity.forEach(r -> r.setIsDelete(true));
            rewardRepository.saveAll(rewardsByEntity);
        } else {
            String errorMessage = "Не найдено ни одной записи с person_id = " + personId;
            kafkaLogFail(errorMessage, personId, message);
        }
    }

    private List<String> getNullRequiredFields(EducationMessageDTO.Action action) {
        List<String> fields = new ArrayList<>();
        if (isNull(action.getAction())) fields.add("action");
        if (action.getAction().equals("createOrUpdate")) {
            if (isNull(action.getContestId())) fields.add("contestId");
            if (isNull(action.getTitle())) fields.add("title");
            if (isNull(action.getLevel())) fields.add("level");
            if (isNull(action.getSubjects())) fields.add("subjects");
            if (isNull(action.getContestStartedAt())) fields.add("contestStartedAt");
            if (isNull(action.getContestFinishedAt())) fields.add("contestFinishedAt");
            if (isNull(action.getParticipatedFrom())) fields.add("participatedFrom");
            if (isNull(action.getParticipatedTo())) fields.add("participatedTo");
            if (isNull(action.getScore())) fields.add("score");
        }
        return fields;
    }
}
