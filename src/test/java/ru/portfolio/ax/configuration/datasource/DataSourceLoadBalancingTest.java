package ru.portfolio.ax.configuration.datasource;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.service.CrudService;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Тест для проверки корректного распределения нагрузки между мастером и репликой
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@SpringJUnitConfig
public class DataSourceLoadBalancingTest {

    @Mock
    private EzdRoutingDataSource routingDataSource;

    @Mock
    private DataSource masterDataSource;

    @Mock
    private DataSource replicaDataSource;

    @Mock
    private Connection masterConnection;

    @Mock
    private Connection replicaConnection;

    @Test
    public void testReadOnlyHolderSetCorrectly() {
        // Тест проверяет, что ReadOnlyHolder корректно устанавливается
        
        // Изначально должно быть false
        assertFalse(EzdRoutingDataSource.isReadOnly());
        
        // Устанавливаем read-only режим
        EzdRoutingDataSource.setReadOnly(true);
        assertTrue(EzdRoutingDataSource.isReadOnly());
        
        // Сбрасываем
        EzdRoutingDataSource.setReadOnly(false);
        assertFalse(EzdRoutingDataSource.isReadOnly());
    }

    @Test
    public void testDetermineCurrentLookupKeyWithReadOnly() {
        // Создаем реальный объект для тестирования
        DataSourceSettings masterSettings = new DataSourceSettings();
        masterSettings.setWeight(1);
        
        DataSourceReplicaProperties replicaProperties = new DataSourceReplicaProperties();
        
        EzdRoutingDataSource realRoutingDataSource = new EzdRoutingDataSource(masterSettings, replicaProperties);
        
        // Тест без read-only режима - должен возвращать null (мастер)
        EzdRoutingDataSource.setReadOnly(false);
        Object lookupKey = realRoutingDataSource.determineCurrentLookupKey();
        assertNull(lookupKey, "Без read-only режима должен использоваться мастер (null)");
        
        // Тест с read-only режимом - должен возвращать ключ реплики
        EzdRoutingDataSource.setReadOnly(true);
        Object readOnlyLookupKey = realRoutingDataSource.determineCurrentLookupKey();
        assertNotNull(readOnlyLookupKey, "В read-only режиме должен использоваться ключ реплики");
        
        // Очищаем состояние
        EzdRoutingDataSource.setReadOnly(false);
    }

    @Test
    public void testWeightedRandomDistribution() {
        // Тест проверяет работу WeightedRandom для распределения нагрузки
        WeightedRandom weightedRandom = new WeightedRandom();
        
        // Добавляем мастер с весом 1
        weightedRandom.addValue(1, 1);
        
        // Добавляем реплику с весом 2
        weightedRandom.addValue(2, 2);
        
        // Проверяем, что значения добавлены
        assertEquals(2, weightedRandom.getValues().size());
        
        // Тестируем случайный выбор (должен возвращать 1 или 2)
        for (int i = 0; i < 10; i++) {
            Integer randomValue = weightedRandom.randomValue();
            assertTrue(randomValue == 1 || randomValue == 2, 
                "Случайное значение должно быть 1 или 2, получено: " + randomValue);
        }
    }

    @Test
    public void testDataSourceConfiguration() {
        // Тест проверяет конфигурацию DataSource
        DataSourceSettings settings = new DataSourceSettings();
        settings.setDriverClassName("org.postgresql.Driver");
        settings.setUrl("*************************************");
        settings.setUsername("test");
        settings.setPassword("test");
        
        DataSourceReplicaProperties replicaProperties = new DataSourceReplicaProperties();
        
        assertTrue(settings.isValid(replicaProperties), "Настройки DataSource должны быть валидными");
    }

    @Test
    public void testReplicaDataSourceIsReadOnly() {
        // Тест проверяет, что реплика настроена как read-only
        ReplicaDataSourceSettings replicaSettings = new ReplicaDataSourceSettings();
        replicaSettings.setDriverClassName("org.postgresql.Driver");
        replicaSettings.setUrl("*************************************");
        replicaSettings.setUsername("test");
        replicaSettings.setPassword("test");
        
        assertTrue(replicaSettings.getShard(), "Реплика должна быть помечена как shard");
        
        // Проверяем, что HikariConfig для реплики настроен как read-only
        var hikariConfig = replicaSettings.createHikariConfig();
        assertTrue(hikariConfig.isReadOnly(), "HikariConfig для реплики должен быть read-only");
    }

    /**
     * Тестовый сервис для проверки аннотаций @ReadOnly
     */
    public static class TestService {
        
        @ReadOnly
        public String readOnlyMethod() {
            return "read-only";
        }
        
        public String writeMethod() {
            return "write";
        }
    }

    @Test
    public void testReadOnlyAnnotationPresent() throws NoSuchMethodException {
        // Проверяем, что аннотация @ReadOnly присутствует на методе
        var method = TestService.class.getMethod("readOnlyMethod");
        assertTrue(method.isAnnotationPresent(ReadOnly.class), 
            "Метод readOnlyMethod должен иметь аннотацию @ReadOnly");
        
        var writeMethod = TestService.class.getMethod("writeMethod");
        assertFalse(writeMethod.isAnnotationPresent(ReadOnly.class), 
            "Метод writeMethod не должен иметь аннотацию @ReadOnly");
    }

    @Test
    public void testThreadLocalReadOnlyHolder() {
        // Тест проверяет работу ThreadLocal в ReadOnlyHolder
        
        // Устанавливаем значение в текущем потоке
        ReadOnlyHolder.set(true);
        assertTrue(ReadOnlyHolder.get(), "ReadOnlyHolder должен возвращать true в текущем потоке");
        
        // Проверяем в другом потоке
        Thread testThread = new Thread(() -> {
            // В новом потоке значение должно быть null (InheritableThreadLocal наследует значение)
            Boolean value = ReadOnlyHolder.get();
            // InheritableThreadLocal должен наследовать значение от родительского потока
            assertTrue(value != null && value, "InheritableThreadLocal должен наследовать значение true");
        });
        
        testThread.start();
        try {
            testThread.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            fail("Поток был прерван");
        }
        
        // Очищаем состояние
        ReadOnlyHolder.set(false);
    }

    @Test
    public void testCurrentReplicaTracking() {
        // Тест проверяет отслеживание текущей реплики
        DataSourceSettings masterSettings = new DataSourceSettings();
        masterSettings.setWeight(1);
        
        DataSourceReplicaProperties replicaProperties = new DataSourceReplicaProperties();
        
        EzdRoutingDataSource realRoutingDataSource = new EzdRoutingDataSource(masterSettings, replicaProperties);
        
        // Устанавливаем read-only режим
        EzdRoutingDataSource.setReadOnly(true);
        
        // Вызываем determineCurrentLookupKey для установки текущей реплики
        realRoutingDataSource.determineCurrentLookupKey();
        
        // Проверяем, что текущая реплика установлена
        Integer currentReplica = EzdRoutingDataSource.getCurrentReplica();
        assertNotNull(currentReplica, "Текущая реплика должна быть установлена");
        
        // Очищаем состояние
        EzdRoutingDataSource.setReadOnly(false);
    }
}
