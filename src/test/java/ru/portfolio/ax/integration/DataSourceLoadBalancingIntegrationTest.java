package ru.portfolio.ax.integration;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import ru.portfolio.ax.configuration.datasource.EzdRoutingDataSource;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.service.ext.NsiService;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Интеграционный тест для проверки реального распределения запросов между мастером и репликой
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DataSourceLoadBalancingIntegrationTest {

    @Autowired
    private EzdRoutingDataSource routingDataSource;

    @Autowired
    private CrudService crudService;

    @Autowired
    private NsiService nsiService;

    @Test
    public void testDataSourceConfiguration() {
        assertNotNull(routingDataSource, "EzdRoutingDataSource должен быть настроен");
        
        Map<Integer, DataSource> dataSources = routingDataSource.getDataSources();
        assertNotNull(dataSources, "DataSources должны быть настроены");
        assertFalse(dataSources.isEmpty(), "Должен быть настроен хотя бы мастер DataSource");
        
        log.info("Настроено DataSources: {}", dataSources.size());
        log.info("Ключи DataSources: {}", dataSources.keySet());
    }

    @Test
    public void testShardDataSourcesConfiguration() {
        var shardKeys = routingDataSource.getShardDataSourcesKeys();
        assertNotNull(shardKeys, "Shard DataSource ключи должны быть настроены");
        
        log.info("Shard DataSource ключи: {}", shardKeys);
        
        if (!shardKeys.isEmpty()) {
            log.info("Реплики настроены и доступны для использования");
        } else {
            log.warn("Реплики не настроены - все запросы будут идти на мастер");
        }
    }

    @Test
    public void testWeightedRandomConfiguration() {
        var weightedRandom = routingDataSource.getRandom();
        assertNotNull(weightedRandom, "WeightedRandom должен быть настроен");
        
        var values = weightedRandom.getValues();
        assertNotNull(values, "WeightedRandom values должны быть настроены");
        assertFalse(values.isEmpty(), "WeightedRandom должен содержать хотя бы одно значение");
        
        log.info("WeightedRandom values: {}", values);
    }

    @Test
    @ReadOnly
    public void testReadOnlyMethodUsesCorrectDataSource() {
        // Этот тест проверяет, что метод с @ReadOnly аннотацией использует правильный DataSource
        
        // Проверяем, что ReadOnlyHolder установлен правильно
        assertTrue(EzdRoutingDataSource.isReadOnly(), 
            "В методе с @ReadOnly аннотацией ReadOnlyHolder должен быть true");
        
        // Получаем ключ для определения DataSource
        Object lookupKey = routingDataSource.determineCurrentLookupKey();
        
        if (routingDataSource.getShardDataSourcesKeys().isEmpty()) {
            // Если реплики не настроены, должен использоваться мастер
            assertNull(lookupKey, "Без реплик должен использоваться мастер (null key)");
            log.info("Реплики не настроены, используется мастер");
        } else {
            // Если реплики настроены, должен использоваться ключ реплики
            assertNotNull(lookupKey, "С настроенными репликами должен использоваться ключ реплики");
            log.info("Используется реплика с ключом: {}", lookupKey);
        }
    }

    @Test
    public void testWriteMethodUsesMaster() {
        // Этот тест проверяет, что метод без @ReadOnly аннотации использует мастер
        
        // Проверяем, что ReadOnlyHolder не установлен
        assertFalse(EzdRoutingDataSource.isReadOnly(), 
            "В методе без @ReadOnly аннотации ReadOnlyHolder должен быть false");
        
        // Получаем ключ для определения DataSource
        Object lookupKey = routingDataSource.determineCurrentLookupKey();
        
        // Должен использоваться мастер (null key)
        assertNull(lookupKey, "Методы записи должны использовать мастер (null key)");
        log.info("Метод записи использует мастер");
    }

    @Test
    public void testMultipleReadOnlyCallsDistribution() {
        // Тест проверяет распределение нескольких read-only вызовов
        
        if (routingDataSource.getShardDataSourcesKeys().isEmpty()) {
            log.info("Пропускаем тест распределения - реплики не настроены");
            return;
        }
        
        AtomicInteger masterCalls = new AtomicInteger(0);
        AtomicInteger replicaCalls = new AtomicInteger(0);
        
        // Выполняем несколько read-only операций
        for (int i = 0; i < 10; i++) {
            executeReadOnlyOperation(masterCalls, replicaCalls);
        }
        
        log.info("Распределение вызовов - Мастер: {}, Реплика: {}", 
            masterCalls.get(), replicaCalls.get());
        
        // Проверяем, что хотя бы некоторые вызовы пошли на реплику
        assertTrue(replicaCalls.get() > 0, 
            "Хотя бы некоторые read-only вызовы должны идти на реплику");
    }

    @ReadOnly
    private void executeReadOnlyOperation(AtomicInteger masterCalls, AtomicInteger replicaCalls) {
        Object lookupKey = routingDataSource.determineCurrentLookupKey();
        
        if (lookupKey == null) {
            masterCalls.incrementAndGet();
        } else {
            replicaCalls.incrementAndGet();
        }
    }

    @Test
    public void testDataSourceConnections() throws SQLException {
        // Тест проверяет, что можно получить соединения от всех настроенных DataSource
        
        Map<Integer, DataSource> dataSources = routingDataSource.getDataSources();
        
        for (Map.Entry<Integer, DataSource> entry : dataSources.entrySet()) {
            Integer key = entry.getKey();
            DataSource dataSource = entry.getValue();
            
            try (Connection connection = dataSource.getConnection()) {
                assertNotNull(connection, "Соединение для DataSource " + key + " должно быть доступно");
                assertFalse(connection.isClosed(), "Соединение для DataSource " + key + " должно быть открыто");
                
                // Проверяем read-only статус для реплик
                if (routingDataSource.getShardDataSourcesKeys().contains(key)) {
                    assertTrue(connection.isReadOnly(), 
                        "Соединение реплики " + key + " должно быть read-only");
                    log.info("DataSource {} (реплика) - read-only: {}", key, connection.isReadOnly());
                } else {
                    log.info("DataSource {} (мастер) - read-only: {}", key, connection.isReadOnly());
                }
            }
        }
    }

    @Test
    @Transactional
    public void testTransactionalMethodBehavior() {
        // Тест проверяет поведение в транзакционном контексте
        
        // В транзакционном методе без @ReadOnly должен использоваться мастер
        assertFalse(EzdRoutingDataSource.isReadOnly(), 
            "В транзакционном методе без @ReadOnly должен использоваться мастер");
        
        Object lookupKey = routingDataSource.determineCurrentLookupKey();
        assertNull(lookupKey, "Транзакционные методы должны использовать мастер");
        
        log.info("Транзакционный метод использует мастер");
    }

    @Test
    public void testCrudServiceReadOnlyMethods() {
        // Тест проверяет, что методы CrudService с @ReadOnly аннотацией работают корректно
        
        try {
            // Вызываем метод с @ReadOnly аннотацией
            // Примечание: этот вызов может завершиться ошибкой если нет данных, но это нормально для теста
            crudService.findFirst(Object.class, "test", "value");
        } catch (Exception e) {
            // Игнорируем ошибки данных, нас интересует только маршрутизация
            log.debug("Ожидаемая ошибка при тестировании маршрутизации: {}", e.getMessage());
        }
        
        log.info("CrudService read-only методы протестированы");
    }

    @Test
    public void testNsiServiceReadOnlyMethods() {
        // Тест проверяет, что методы NsiService с @ReadOnly аннотацией работают корректно
        
        try {
            // Вызываем метод с @ReadOnly аннотацией
            nsiService.getTeachers(1, null, 10);
        } catch (Exception e) {
            // Игнорируем ошибки данных, нас интересует только маршрутизация
            log.debug("Ожидаемая ошибка при тестировании маршрутизации: {}", e.getMessage());
        }
        
        log.info("NsiService read-only методы протестированы");
    }

    @Test
    public void testReadOnlyHolderThreadSafety() throws InterruptedException {
        // Тест проверяет потокобезопасность ReadOnlyHolder
        
        final int threadCount = 5;
        final AtomicInteger successCount = new AtomicInteger(0);
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                try {
                    // Каждый поток устанавливает свое значение
                    boolean readOnlyValue = threadId % 2 == 0;
                    EzdRoutingDataSource.setReadOnly(readOnlyValue);
                    
                    // Проверяем, что значение установлено корректно
                    if (EzdRoutingDataSource.isReadOnly() == readOnlyValue) {
                        successCount.incrementAndGet();
                    }
                    
                    Thread.sleep(10); // Небольшая задержка
                    
                    // Проверяем еще раз
                    if (EzdRoutingDataSource.isReadOnly() == readOnlyValue) {
                        successCount.incrementAndGet();
                    }
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // Запускаем все потоки
        for (Thread thread : threads) {
            thread.start();
        }
        
        // Ждем завершения всех потоков
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Проверяем, что все потоки работали корректно
        assertEquals(threadCount * 2, successCount.get(), 
            "Все потоки должны корректно работать с ReadOnlyHolder");
        
        log.info("Тест потокобезопасности ReadOnlyHolder завершен успешно");
    }
}
